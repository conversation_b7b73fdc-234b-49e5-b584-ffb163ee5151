terraform {
  required_providers {
    aws = {
      version = "3.76.0"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

data "terraform_remote_state" "selected_webstack" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/${var.onCluster ? "consumer-webstack-cluster" : "consumer-webstack"}"
    region = var.region
  }
}
module "route53_record" {
  source                 = "../../../modules/consumer-webstack-routing"
  consumer_hosted_zone_id = var.external_hosted_zone_id
  alb_dns_name           = data.terraform_remote_state.selected_webstack.outputs.consumer-webstack_alb_dns_name
  alb_zone_id            = data.terraform_remote_state.selected_webstack.outputs.consumer-webstack_alb_zone_id
  alb_route53_suffix = "-${var.environment}.us.consumer"
}



terraform {
  required_providers {
    aws = {
      version = "3.75.2"
      source = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

data "terraform_remote_state" "sns-alerts" {
  backend = "s3"

  config = {
    bucket = "ais.prod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/sns-alerts"
    region = var.region
  }
}

data "terraform_remote_state" "rrri" {
  backend = "s3"

  config = {
    bucket = "ais.prod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/rrri"
    region = var.region
  }
}


module "internal-webstack-application-US" {
  source = "../../../modules/internal-webstack-apps/task-definitions"

  country_iso_code          = "US"
  task_friendly_name        = "US_InternalWebstackApplication"
  container_definition_path = "../../../modules/internal-webstack-apps/task-definitions/container-definitions/InternalWebstackApplication.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/web_app_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/10 * * * ? *" #run every 10 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"
  rrri_topic_arn            = data.terraform_remote_state.rrri.outputs.topic_arn

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
  application_name = "InternalWebstack"
}


module "internal-webstack-application-CA" {
  source = "../../../modules/internal-webstack-apps/task-definitions"

  country_iso_code          = "CA"
  task_friendly_name        = "CA_InternalWebstackApplication"
  container_definition_path = "../../../modules/internal-webstack-apps/task-definitions/container-definitions/InternalWebstackApplication.json"
  task_role_arn             = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn        = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  image_url_name_tag        = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/web_app_${var.environment}:latest"
  ini_bucket                = var.package_bucket_names[var.region]
  retention_in_days         = 7
  schedule_expression       = "0/10 * * * ? *" #run every 10 minutes every day
  event_rule_arn            = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-invoke-tasks"
  rrri_topic_arn            = data.terraform_remote_state.rrri.outputs.topic_arn

  application      = var.application
  service          = var.service
  region           = var.region
  environment      = var.environment
  build_number     = var.build_number
  launched_by      = var.launched_by
  launched_on      = var.launched_on
  slack_contact    = var.slack_contact
  component        = var.component
  enabled          = false
  alarm_action_arn = data.terraform_remote_state.sns-alerts.outputs.warning_alert_arn
  application_name = "InternalWebstack"
}
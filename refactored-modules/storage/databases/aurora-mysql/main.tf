# Aurora MySQL Database Module
# This module creates an Aurora MySQL cluster using the official Terraform AWS modules

###############################################################
# Random Password Generation (if not using managed passwords)
###############################################################

resource "random_password" "master_password" {
  count   = var.manage_master_user_password ? 0 : (var.master_password == null ? 1 : 0)
  length  = 16
  special = true
}

###############################################################
# DB Subnet Group
###############################################################

resource "aws_db_subnet_group" "this" {
  name       = "${local.cluster_identifier}-subnet-group"
  subnet_ids = var.subnet_ids

  tags = merge(
    local.common_tags,
    {
      Name = "dbsng-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "DBSubnetGroup"
    }
  )
}

###############################################################
# DB Parameter Groups
###############################################################

resource "aws_rds_cluster_parameter_group" "this" {
  count       = var.db_cluster_parameter_group_name == null && length(var.cluster_parameters) > 0 ? 1 : 0
  name        = "${local.cluster_identifier}-cluster-params"
  family      = "aurora-mysql8.0"
  description = "Cluster parameter group for ${local.cluster_identifier}"

  dynamic "parameter" {
    for_each = var.cluster_parameters
    content {
      name         = parameter.value.name
      value        = parameter.value.value
      apply_method = parameter.value.apply_method
    }
  }

  tags = merge(
    local.common_tags,
    {
      Name = "cpg-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "ClusterParameterGroup"
    }
  )

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_db_parameter_group" "this" {
  count       = var.db_parameter_group_name == null && length(var.instance_parameters) > 0 ? 1 : 0
  name        = "${local.cluster_identifier}-instance-params"
  family      = "aurora-mysql8.0"
  description = "Instance parameter group for ${local.cluster_identifier}"

  dynamic "parameter" {
    for_each = var.instance_parameters
    content {
      name         = parameter.value.name
      value        = parameter.value.value
      apply_method = parameter.value.apply_method
    }
  }

  tags = merge(
    local.common_tags,
    {
      Name = "ipg-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "InstanceParameterGroup"
    }
  )

  lifecycle {
    create_before_destroy = true
  }
}

###############################################################
# Aurora MySQL Cluster using Official Module
###############################################################

module "aurora" {
  source  = "terraform-aws-modules/rds-aurora/aws"
  version = "~> 8.0"

  name               = local.cluster_identifier
  engine             = "aurora-mysql"
  engine_version     = var.engine_version
  database_name      = var.database_name
  master_username    = var.master_username

  # Password management
  manage_master_user_password = var.manage_master_user_password
  master_password            = var.manage_master_user_password ? null : (var.master_password != null ? var.master_password : random_password.master_password[0].result)

  # Network configuration
  vpc_id               = var.vpc_id
  db_subnet_group_name = aws_db_subnet_group.this.name
  vpc_security_group_ids = var.security_group_ids

  # Instance configuration
  instance_class = var.instance_class
  instances      = var.instances

  # Auto scaling configuration
  autoscaling_enabled      = var.replica_count > 0
  autoscaling_min_capacity = 1
  autoscaling_max_capacity = var.replica_count + 1

  # Backup and maintenance
  backup_retention_period      = var.backup_retention_period
  preferred_backup_window      = var.preferred_backup_window
  preferred_maintenance_window = var.preferred_maintenance_window
  copy_tags_to_snapshot       = var.copy_tags_to_snapshot
  skip_final_snapshot         = var.skip_final_snapshot
  final_snapshot_identifier   = var.skip_final_snapshot ? null : "${local.cluster_identifier}-${var.final_snapshot_identifier_prefix}-${formatdate("YYYY-MM-DD-hhmm", timestamp())}"

  # Snapshot and restore
  snapshot_identifier       = var.snapshot_identifier
  restore_to_point_in_time = var.restore_to_point_in_time

  # Performance and monitoring
  performance_insights_enabled          = var.performance_insights_enabled
  performance_insights_retention_period = var.performance_insights_retention_period
  monitoring_interval                   = var.monitoring_interval
  enabled_cloudwatch_logs_exports       = var.enabled_cloudwatch_logs_exports

  # Security
  storage_encrypted   = var.storage_encrypted
  kms_key_id         = var.kms_key_id
  deletion_protection = var.deletion_protection

  # Parameter groups
  db_cluster_parameter_group_name = var.db_cluster_parameter_group_name != null ? var.db_cluster_parameter_group_name : (length(aws_rds_cluster_parameter_group.this) > 0 ? aws_rds_cluster_parameter_group.this[0].name : null)
  db_parameter_group_name        = var.db_parameter_group_name != null ? var.db_parameter_group_name : (length(aws_db_parameter_group.this) > 0 ? aws_db_parameter_group.this[0].name : null)

  # Engine configuration
  auto_minor_version_upgrade   = var.auto_minor_version_upgrade
  allow_major_version_upgrade  = var.allow_major_version_upgrade
  apply_immediately           = var.apply_immediately

  # Tags
  tags = local.common_tags

  depends_on = [
    aws_db_subnet_group.this,
    aws_rds_cluster_parameter_group.this,
    aws_db_parameter_group.this
  ]
}

###############################################################
# CloudWatch Log Groups
###############################################################

resource "aws_cloudwatch_log_group" "this" {
  for_each = toset(var.enabled_cloudwatch_logs_exports)

  name              = "/aws/rds/cluster/${local.cluster_identifier}/${each.value}"
  retention_in_days = 30

  tags = merge(
    local.common_tags,
    {
      Name = "lg-${each.value}-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "LogGroup"
      LogType = each.value
    }
  )
}

###############################################################
# Route53 DNS Records (if internal domain is configured)
###############################################################

data "aws_route53_zone" "internal" {
  count        = var.create_dns_record ? 1 : 0
  name         = var.internal_domain
  private_zone = true
}

resource "aws_route53_record" "cluster_writer" {
  count   = var.create_dns_record ? 1 : 0
  zone_id = data.aws_route53_zone.internal[0].zone_id
  name    = "${local.cluster_identifier}-writer"
  type    = "CNAME"
  ttl     = 300
  records = [module.aurora.cluster_endpoint]

  tags = merge(
    local.common_tags,
    {
      Name = "dns-writer-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "DNSRecord"
      Purpose = "DatabaseWriter"
    }
  )
}

resource "aws_route53_record" "cluster_reader" {
  count   = var.create_dns_record ? 1 : 0
  zone_id = data.aws_route53_zone.internal[0].zone_id
  name    = "${local.cluster_identifier}-reader"
  type    = "CNAME"
  ttl     = 300
  records = [module.aurora.cluster_reader_endpoint]

  tags = merge(
    local.common_tags,
    {
      Name = "dns-reader-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "DNSRecord"
      Purpose = "DatabaseReader"
    }
  )
}

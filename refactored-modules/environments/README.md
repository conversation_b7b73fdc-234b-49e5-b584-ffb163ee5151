# Environment Abstraction Framework

This directory contains the environment abstraction framework that enables consistent, scalable deployment of infrastructure across multiple environments using the refactored modules.

## Overview

The environment abstraction framework provides:
- **Shared Configurations**: Common patterns across environments
- **Environment-Specific Overrides**: Customization for each environment
- **Workspace-Based Deployment**: Terraform workspace integration
- **Consistent Variable Management**: Standardized variable handling
- **Deployment Automation**: CI/CD integration patterns

## Directory Structure

```
environments/
├── README.md                           # This file
├── shared/                            # Shared configurations
│   ├── backend.tf                     # Remote state configuration
│   ├── providers.tf                   # Provider configurations
│   ├── variables.tf                   # Common variables
│   └── locals.tf                      # Shared local values
├── templates/                         # Environment templates
│   ├── main.tf                        # Main infrastructure template
│   ├── variables.tf                   # Environment-specific variables
│   ├── outputs.tf                     # Standard outputs
│   └── terraform.tfvars.example       # Example variable values
├── configs/                           # Environment-specific configurations
│   ├── production.tfvars              # Production environment
│   ├── nonprod.tfvars                 # Non-production environment
│   ├── qamain.tfvars                  # QA main environment
│   ├── qarapid.tfvars                 # QA rapid environment
│   ├── uat.tfvars                     # UAT environment
│   ├── scratch.tfvars                 # Scratch environment
│   └── homenet.tfvars                 # HomeNet environment
├── workspaces/                        # Workspace management
│   ├── create-workspace.sh            # Workspace creation script
│   ├── switch-workspace.sh            # Workspace switching script
│   └── list-workspaces.sh             # Workspace listing script
└── deployment/                        # Deployment automation
    ├── deploy.sh                      # Deployment script
    ├── plan.sh                        # Planning script
    ├── destroy.sh                     # Destruction script
    └── validate.sh                    # Validation script
```

## Key Concepts

### 1. **Environment Abstraction**
Each environment uses the same infrastructure template but with different variable values:
- Consistent architecture across environments
- Environment-specific sizing and configuration
- Shared security and compliance patterns

### 2. **Workspace-Based Deployment**
Terraform workspaces separate state for each environment:
- Isolated state management
- Parallel environment operations
- Simplified deployment workflows

### 3. **Configuration Management**
Environment-specific `.tfvars` files contain all customizations:
- Centralized configuration management
- Version-controlled environment settings
- Easy environment comparison

### 4. **Shared Components**
Common configurations reduce duplication:
- Shared provider configurations
- Common variable definitions
- Reusable local values

## Usage Patterns

### 1. **New Environment Deployment**
```bash
# Create workspace
./workspaces/create-workspace.sh production

# Deploy infrastructure
./deployment/deploy.sh production
```

### 2. **Environment Updates**
```bash
# Plan changes
./deployment/plan.sh production

# Apply changes
./deployment/deploy.sh production
```

### 3. **Environment Comparison**
```bash
# Compare configurations
diff configs/production.tfvars configs/nonprod.tfvars
```

## Environment Configurations

### Production Environment
- High availability across multiple AZs
- Enhanced security and monitoring
- Larger instance sizes and storage
- Automated backups and disaster recovery

### Non-Production Environment
- Cost-optimized configurations
- Reduced redundancy for development
- Smaller instance sizes
- Shorter backup retention

### QA Environments
- Isolated testing environments
- Configurable for different test scenarios
- Easy reset and recreation capabilities
- Integration with CI/CD pipelines

### Development Environments
- Minimal resource allocation
- Rapid deployment and teardown
- Developer-specific customizations
- Cost controls and limits

## Benefits

### 1. **Consistency**
- Same infrastructure patterns across environments
- Reduced configuration drift
- Standardized deployment processes

### 2. **Efficiency**
- Shared configurations reduce duplication
- Automated deployment processes
- Parallel environment management

### 3. **Reliability**
- Tested patterns across environments
- Isolated state management
- Rollback capabilities

### 4. **Cost Management**
- Environment-appropriate sizing
- Resource optimization per environment
- Cost tracking and controls

## Integration with Refactored Modules

The environment framework integrates seamlessly with the refactored modules:

```hcl
# Example environment template
module "networking" {
  source = "../../refactored-modules/foundation/networking/data-sources"
  
  # Common variables from shared configuration
  application = local.common_config.application
  environment = local.environment_config.name
  region      = local.common_config.region
  
  # Environment-specific variables
  vpc_name = var.vpc_name
}

module "security_groups" {
  source = "../../refactored-modules/foundation/security/security-groups"
  
  # Pass-through common variables
  application             = local.common_config.application
  application_abbreviated = local.common_config.application_abbreviated
  # ... other variables
  
  # Environment-specific security configuration
  security_groups = var.security_groups_config
}
```

## Migration Strategy

### Phase 1: Framework Setup
1. Create shared configurations
2. Set up workspace management
3. Create environment templates
4. Test with development environment

### Phase 2: Environment Migration
1. Start with non-production environments
2. Migrate one environment at a time
3. Validate functionality after each migration
4. Update CI/CD pipelines

### Phase 3: Production Migration
1. Thorough testing in staging
2. Blue-green deployment strategy
3. Rollback procedures ready
4. Monitoring and validation

## Best Practices

### 1. **Configuration Management**
- Use descriptive variable names
- Include validation rules
- Document all environment-specific settings
- Version control all configurations

### 2. **State Management**
- Use remote state storage
- Enable state locking
- Regular state backups
- Workspace naming conventions

### 3. **Security**
- Encrypt state files
- Secure variable handling
- Access controls for environments
- Audit trail for changes

### 4. **Monitoring**
- Track deployment metrics
- Monitor environment health
- Alert on configuration drift
- Regular compliance checks

## Getting Started

1. **Review Shared Configurations**: Understand common patterns
2. **Choose Environment Template**: Select appropriate template
3. **Customize Variables**: Create environment-specific configuration
4. **Deploy and Validate**: Test deployment in development first
5. **Integrate with CI/CD**: Automate deployment processes

This framework provides the foundation for scalable, consistent infrastructure deployment while maintaining the flexibility to customize each environment according to its specific requirements.

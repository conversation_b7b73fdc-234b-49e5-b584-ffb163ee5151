# Non-Production Environment Configuration
# This file contains non-production specific variable values

###############################################################
# Core Application Configuration
###############################################################

application             = "ais"
application_abbreviated = "ais"
environment            = "nonprod"
region                 = "us-east-1"
region_abbreviated     = "ue1"
build_number           = "1.0.0"
launched_by            = "terraform"
launched_on            = "2024-01-01T00:00:00Z"
slack_contact          = "+dev-team"

###############################################################
# Environment-Specific Configuration
###############################################################

vpc_name               = "ais-nonprod-vpc"
cost_center           = "engineering"
data_classification   = "internal"
compliance_framework  = ""

###############################################################
# Feature Flags for Non-Production
###############################################################

feature_flags = {
  enable_waf                = false
  enable_shield_advanced    = false
  enable_secrets_manager    = true
  enable_parameter_store    = true
  enable_systems_manager    = true
  enable_inspector         = false
  enable_macie             = false
  enable_security_hub      = false
}

###############################################################
# Component Enablement
###############################################################

enable_database      = true
enable_load_balancer = true
enable_web_servers   = true

###############################################################
# Security Groups Configuration (More Permissive for Development)
###############################################################

security_groups_config = {
  load-balancer = {
    description = "Non-prod load balancer security group"
    ingress_rules = [
      {
        description = "HTTPS from internet"
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      },
      {
        description = "HTTP from internet"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
    egress_rules = [
      {
        description = "All outbound traffic"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
  }

  web-server = {
    description = "Non-prod web server security group"
    ingress_rules = [
      {
        description = "HTTP from load balancer"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      },
      {
        description = "SSH from trusted networks"
        from_port   = 22
        to_port     = 22
        protocol    = "tcp"
        cidr_blocks = ["************/32", "***********/32", "10.0.0.0/8"]
      },
      {
        description = "Development ports"
        from_port   = 3000
        to_port     = 9000
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      }
    ]
    egress_rules = [
      {
        description = "All outbound traffic"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
  }

  database = {
    description = "Non-prod database security group"
    ingress_rules = [
      {
        description = "MySQL from web servers"
        from_port   = 3306
        to_port     = 3306
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      },
      {
        description = "MySQL from trusted networks"
        from_port   = 3306
        to_port     = 3306
        protocol    = "tcp"
        cidr_blocks = ["************/32", "***********/32"]
      }
    ]
    egress_rules = [
      {
        description = "All outbound traffic"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
  }

  lambda = {
    description = "Non-prod Lambda security group"
    ingress_rules = []
    egress_rules = [
      {
        description = "All outbound traffic"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
  }
}

###############################################################
# Database Configuration (Smaller for Non-Prod)
###############################################################

database_config = {
  component_name                  = "ais-db"
  database_name                   = "ais_nonprod"
  master_username                 = "ais_admin"
  replica_count                   = 0  # No replicas for non-prod
  kms_key_id                     = ""
  enabled_cloudwatch_logs_exports = ["error"]  # Minimal logging
}

###############################################################
# Load Balancer Configuration
###############################################################

load_balancer_config = {
  component_name  = "ais-frontend"
  internal        = false
  certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/nonprod-cert-id"

  target_groups = {
    web-servers = {
      port     = 80
      protocol = "HTTP"
      health_check = {
        path                = "/health"
        healthy_threshold   = 2
        unhealthy_threshold = 5  # More lenient for development
        timeout             = 10
        interval            = 60
        matcher             = "200"
      }
      stickiness = {
        enabled = false
      }
    }
  }

  listeners = {
    http = {
      port     = 80
      protocol = "HTTP"
      default_action = {
        type             = "forward"
        target_group_arn = "web-servers"
      }
    }
    https = {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/nonprod-cert-id"
      default_action = {
        type             = "forward"
        target_group_arn = "web-servers"
      }
    }
  }

  waf_config = {
    enable_rate_limiting = false
    rate_limit          = 10000
    enable_geo_blocking = false
    blocked_countries   = []
  }
}

###############################################################
# Web Servers Configuration (Smaller Instances)
###############################################################

web_servers_config = {
  component_name     = "ais-web"
  key_name          = "ais-nonprod-key"
  base_capacity     = 1  # Single instance for non-prod
  root_volume_size  = 30
  cpu_high_threshold = 80
  cpu_low_threshold  = 20
  target_group_name = "web-servers"
  user_data_script  = ""
}

###############################################################
# Lambda Functions Configuration (Simplified)
###############################################################

lambda_functions_config = {
  api-processor = {
    service       = "api"
    component     = "processor"
    function_name = "ais-api-processor"
    description   = "AIS API request processor (non-prod)"
    runtime       = "python3.11"
    handler       = "app.lambda_handler"
    timeout       = 30
    memory_size   = 256  # Smaller memory for non-prod

    source_path = "../lambda-code/api-processor"

    environment_variables = {
      LOG_LEVEL     = "DEBUG"
      DATABASE_URL  = "mysql://ais-nonprod.cluster-xxx.us-east-1.rds.amazonaws.com:3306/ais_nonprod"
      CORS_ORIGINS  = "*"  # Allow all origins for development
      CACHE_TTL     = "60"
    }

    vpc_enabled = false  # No VPC for simpler development

    policy_statements = {
      rds_access = {
        effect = "Allow"
        actions = [
          "rds-db:connect"
        ]
        resources = [
          "arn:aws:rds-db:us-east-1:*:dbuser:*/lambda-user"
        ]
      }
    }
  }

  data-processor = {
    service       = "processing"
    component     = "data-processor"
    function_name = "ais-data-processor"
    description   = "AIS data processing function (non-prod)"
    runtime       = "python3.11"
    handler       = "processor.lambda_handler"
    timeout       = 120  # Shorter timeout for non-prod
    memory_size   = 512

    source_path = "../lambda-code/data-processor"

    environment_variables = {
      LOG_LEVEL      = "DEBUG"
      OUTPUT_BUCKET  = "ais-nonprod-processed-data"
      BATCH_SIZE     = "10"  # Smaller batches for testing
      RETRY_ATTEMPTS = "1"
    }

    vpc_enabled = false

    policy_statements = {
      s3_access = {
        effect = "Allow"
        actions = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        resources = [
          "arn:aws:s3:::ais-nonprod-processed-data/*"
        ]
      }
    }
  }
}

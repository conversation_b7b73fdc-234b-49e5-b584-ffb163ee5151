# Production Environment Configuration
# This file contains production-specific variable values

###############################################################
# Core Application Configuration
###############################################################

application             = "ais"
application_abbreviated = "ais"
environment            = "production"
region                 = "us-east-1"
region_abbreviated     = "ue1"
build_number           = "1.0.0"
launched_by            = "terraform"
launched_on            = "2024-01-01T00:00:00Z"
slack_contact          = "+ops-team"

###############################################################
# Environment-Specific Configuration
###############################################################

vpc_name               = "ais-production-vpc"
cost_center           = "engineering"
data_classification   = "confidential"
compliance_framework  = "SOC2"

###############################################################
# Feature Flags for Production
###############################################################

feature_flags = {
  enable_waf                = true
  enable_shield_advanced    = true
  enable_secrets_manager    = true
  enable_parameter_store    = true
  enable_systems_manager    = true
  enable_inspector         = true
  enable_macie             = true
  enable_security_hub      = true
}

###############################################################
# Component Enablement
###############################################################

enable_database      = true
enable_load_balancer = true
enable_web_servers   = true

###############################################################
# Security Groups Configuration
###############################################################

security_groups_config = {
  load-balancer = {
    description = "Production load balancer security group"
    ingress_rules = [
      {
        description = "HTTPS from internet"
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      },
      {
        description = "HTTP redirect to HTTPS"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
    egress_rules = [
      {
        description = "All outbound to web servers"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      },
      {
        description = "HTTPS outbound for health checks"
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      }
    ]
  }

  web-server = {
    description = "Production web server security group"
    ingress_rules = [
      {
        description = "HTTP from load balancer"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      },
      {
        description = "HTTPS from load balancer"
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      },
      {
        description = "SSH from bastion hosts only"
        from_port   = 22
        to_port     = 22
        protocol    = "tcp"
        cidr_blocks = ["********/24"]  # Bastion subnet
      }
    ]
    egress_rules = [
      {
        description = "HTTPS outbound for updates"
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      },
      {
        description = "HTTP outbound for package updates"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      },
      {
        description = "MySQL to database"
        from_port   = 3306
        to_port     = 3306
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      }
    ]
  }

  database = {
    description = "Production database security group"
    ingress_rules = [
      {
        description = "MySQL from web servers"
        from_port   = 3306
        to_port     = 3306
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      },
      {
        description = "MySQL from management subnet"
        from_port   = 3306
        to_port     = 3306
        protocol    = "tcp"
        cidr_blocks = ["********/24"]
      }
    ]
    egress_rules = []  # No outbound access needed for database
  }

  lambda = {
    description = "Production Lambda security group"
    ingress_rules = []
    egress_rules = [
      {
        description = "HTTPS outbound for API calls"
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      },
      {
        description = "MySQL to database"
        from_port   = 3306
        to_port     = 3306
        protocol    = "tcp"
        cidr_blocks = ["10.0.0.0/8"]
      }
    ]
  }
}

###############################################################
# Database Configuration
###############################################################

database_config = {
  component_name                  = "ais-db"
  database_name                   = "ais_production"
  master_username                 = "ais_admin"
  replica_count                   = 2
  kms_key_id                     = ""  # Will use default AWS managed key
  enabled_cloudwatch_logs_exports = ["audit", "error", "general", "slowquery"]
}

###############################################################
# Load Balancer Configuration
###############################################################

load_balancer_config = {
  component_name  = "ais-frontend"
  internal        = false
  certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"

  target_groups = {
    web-servers = {
      port     = 80
      protocol = "HTTP"
      health_check = {
        path                = "/health"
        healthy_threshold   = 2
        unhealthy_threshold = 3
        timeout             = 5
        interval            = 30
        matcher             = "200"
      }
      stickiness = {
        enabled = false
      }
    }
    api-servers = {
      port     = 8080
      protocol = "HTTP"
      health_check = {
        path                = "/api/health"
        healthy_threshold   = 2
        unhealthy_threshold = 3
        timeout             = 5
        interval            = 30
        matcher             = "200"
      }
      stickiness = {
        enabled = false
      }
    }
  }

  listeners = {
    http = {
      port     = 80
      protocol = "HTTP"
      default_action = {
        type = "redirect"
        redirect = {
          port        = "443"
          protocol    = "HTTPS"
          status_code = "HTTP_301"
        }
      }
    }
    https = {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
      default_action = {
        type             = "forward"
        target_group_arn = "web-servers"
      }
    }
  }

  waf_config = {
    enable_rate_limiting = true
    rate_limit          = 2000
    enable_geo_blocking = false
    blocked_countries   = []
  }
}

###############################################################
# Web Servers Configuration
###############################################################

web_servers_config = {
  component_name     = "ais-web"
  key_name          = "ais-production-key"
  base_capacity     = 2
  root_volume_size  = 50
  cpu_high_threshold = 70
  cpu_low_threshold  = 30
  target_group_name = "web-servers"
  user_data_script  = ""  # Will use default from module
}

###############################################################
# Lambda Functions Configuration
###############################################################

lambda_functions_config = {
  api-processor = {
    service       = "api"
    component     = "processor"
    function_name = "ais-api-processor"
    description   = "AIS API request processor"
    runtime       = "python3.11"
    handler       = "app.lambda_handler"
    timeout       = 30
    memory_size   = 512

    source_path = "../lambda-code/api-processor"

    environment_variables = {
      LOG_LEVEL     = "INFO"
      DATABASE_URL  = "mysql://ais-production.cluster-xxx.us-east-1.rds.amazonaws.com:3306/ais_production"
      CORS_ORIGINS  = "https://ais.example.com"
      CACHE_TTL     = "300"
    }

    vpc_enabled = true

    additional_iam_policies = [
      "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
    ]

    policy_statements = {
      rds_access = {
        effect = "Allow"
        actions = [
          "rds-db:connect"
        ]
        resources = [
          "arn:aws:rds-db:us-east-1:*:dbuser:*/lambda-user"
        ]
      }
      secrets_access = {
        effect = "Allow"
        actions = [
          "secretsmanager:GetSecretValue"
        ]
        resources = [
          "arn:aws:secretsmanager:us-east-1:*:secret:ais/production/*"
        ]
      }
    }
  }

  data-processor = {
    service       = "processing"
    component     = "data-processor"
    function_name = "ais-data-processor"
    description   = "AIS data processing function"
    runtime       = "python3.11"
    handler       = "processor.lambda_handler"
    timeout       = 300
    memory_size   = 1024

    source_path = "../lambda-code/data-processor"

    environment_variables = {
      LOG_LEVEL      = "INFO"
      OUTPUT_BUCKET  = "ais-production-processed-data"
      BATCH_SIZE     = "100"
      RETRY_ATTEMPTS = "3"
    }

    vpc_enabled = false

    policy_statements = {
      s3_access = {
        effect = "Allow"
        actions = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        resources = [
          "arn:aws:s3:::ais-production-processed-data/*"
        ]
      }
    }
  }
}

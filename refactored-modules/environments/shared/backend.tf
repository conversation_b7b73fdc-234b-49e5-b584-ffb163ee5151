# Shared Backend Configuration
# This file defines the remote state backend configuration for all environments

terraform {
  # Backend configuration for remote state storage
  backend "s3" {
    # S3 bucket for state storage (to be configured per deployment)
    # bucket = "ais-terraform-state-${var.region}-${var.environment}"
    
    # State file path with workspace support
    # key = "infrastructure/${terraform.workspace}/terraform.tfstate"
    
    # AWS region for state bucket
    # region = "us-east-1"
    
    # DynamoDB table for state locking
    # dynamodb_table = "ais-terraform-locks"
    
    # Enable encryption for state files
    encrypt = true
    
    # Enable versioning for state files
    versioning = true
    
    # Server-side encryption configuration
    server_side_encryption_configuration {
      rule {
        apply_server_side_encryption_by_default {
          sse_algorithm = "AES256"
        }
      }
    }
  }
}

# Data source to get current workspace
data "terraform_remote_state" "current" {
  backend   = "s3"
  workspace = terraform.workspace
  
  config = {
    bucket = local.state_bucket
    key    = "infrastructure/${terraform.workspace}/terraform.tfstate"
    region = local.state_region
  }
}

# Local values for backend configuration
locals {
  # Determine state bucket based on region and environment
  state_bucket = "ais-terraform-state-${local.region_abbreviated}-${terraform.workspace}"
  
  # State storage region (typically same as deployment region)
  state_region = var.region
  
  # DynamoDB table for state locking
  lock_table = "ais-terraform-locks-${local.region_abbreviated}"
  
  # Workspace-based state key
  state_key = "infrastructure/${terraform.workspace}/terraform.tfstate"
  
  # Backend configuration for workspace creation
  backend_config = {
    bucket         = local.state_bucket
    key           = local.state_key
    region        = local.state_region
    dynamodb_table = local.lock_table
    encrypt       = true
  }
}

# Output backend configuration for reference
output "backend_configuration" {
  description = "Backend configuration details"
  value = {
    workspace      = terraform.workspace
    state_bucket   = local.state_bucket
    state_key      = local.state_key
    state_region   = local.state_region
    lock_table     = local.lock_table
    encryption     = true
  }
}

# Data source for current AWS account
data "aws_caller_identity" "current" {}

# Data source for current AWS region
data "aws_region" "current" {}

# Data source for available AZs
data "aws_availability_zones" "available" {
  state = "available"
}

# Common data sources that all environments need
output "aws_account_id" {
  description = "Current AWS account ID"
  value       = data.aws_caller_identity.current.account_id
}

output "aws_region" {
  description = "Current AWS region"
  value       = data.aws_region.current.name
}

output "availability_zones" {
  description = "Available availability zones"
  value       = data.aws_availability_zones.available.names
}

# Workspace validation
locals {
  valid_workspaces = [
    "production",
    "nonprod", 
    "qamain",
    "qarapid",
    "uat",
    "scratch",
    "homenet"
  ]
  
  is_valid_workspace = contains(local.valid_workspaces, terraform.workspace)
}

# Validation check for workspace
resource "null_resource" "workspace_validation" {
  count = local.is_valid_workspace ? 0 : 1
  
  provisioner "local-exec" {
    command = <<-EOT
      echo "ERROR: Invalid workspace '${terraform.workspace}'"
      echo "Valid workspaces are: ${join(", ", local.valid_workspaces)}"
      exit 1
    EOT
  }
}

# State bucket creation (if needed)
resource "aws_s3_bucket" "terraform_state" {
  count  = var.create_state_bucket ? 1 : 0
  bucket = local.state_bucket

  tags = {
    Name        = local.state_bucket
    Purpose     = "TerraformState"
    Environment = terraform.workspace
    ManagedBy   = "terraform"
  }

  lifecycle {
    prevent_destroy = true
  }
}

# State bucket versioning
resource "aws_s3_bucket_versioning" "terraform_state" {
  count  = var.create_state_bucket ? 1 : 0
  bucket = aws_s3_bucket.terraform_state[0].id
  
  versioning_configuration {
    status = "Enabled"
  }
}

# State bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "terraform_state" {
  count  = var.create_state_bucket ? 1 : 0
  bucket = aws_s3_bucket.terraform_state[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# State bucket public access block
resource "aws_s3_bucket_public_access_block" "terraform_state" {
  count  = var.create_state_bucket ? 1 : 0
  bucket = aws_s3_bucket.terraform_state[0].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# DynamoDB table for state locking
resource "aws_dynamodb_table" "terraform_locks" {
  count          = var.create_lock_table ? 1 : 0
  name           = local.lock_table
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "LockID"

  attribute {
    name = "LockID"
    type = "S"
  }

  tags = {
    Name        = local.lock_table
    Purpose     = "TerraformStateLocking"
    Environment = terraform.workspace
    ManagedBy   = "terraform"
  }

  lifecycle {
    prevent_destroy = true
  }
}

# Shared Variables Configuration
# This file defines variables that are common across all environments

###############################################################
# Core Application Variables (Required for all environments)
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z0-9]{2,5}$", var.application_abbreviated))
    error_message = "Application abbreviation must be 2-5 characters, lowercase letters and numbers only."
  }
}

variable "environment" {
  description = "Name of the environment"
  type        = string
  
  validation {
    condition = contains([
      "production", "nonprod", "homenet", "qamain", 
      "qarapid", "scratch", "uat"
    ], var.environment)
    error_message = "Environment must be one of: production, nonprod, homenet, qamain, qarapid, scratch, uat."
  }
}

variable "region" {
  description = "AWS Region"
  type        = string
  
  validation {
    condition = contains([
      "us-east-1", "us-west-2"
    ], var.region)
    error_message = "Region must be one of the supported regions: us-east-1, us-west-2."
  }
}

variable "region_abbreviated" {
  description = "Abbreviated form of the AWS region"
  type        = string
  
  validation {
    condition = contains([
      "ue1", "uw2"
    ], var.region_abbreviated)
    error_message = "Region abbreviation must be one of: ue1, uw2."
  }
}

variable "build_number" {
  description = "Build Number for tracking deployments"
  type        = string
  
  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.build_number))
    error_message = "Build number must be in semantic version format (e.g., 1.0.0)."
  }
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string
  
  validation {
    condition     = length(var.launched_by) > 0
    error_message = "Launched by cannot be empty."
  }
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string
  
  validation {
    condition     = can(formatdate("RFC3339", var.launched_on))
    error_message = "Launched on must be a valid RFC3339 timestamp."
  }
}

variable "slack_contact" {
  description = "Slack channel for notifications"
  type        = string
  
  validation {
    condition     = can(regex("^[+#@][a-zA-Z0-9-_]+$", var.slack_contact))
    error_message = "Slack contact must start with +, #, or @ followed by valid channel/user name."
  }
}

###############################################################
# Provider Configuration Variables
###############################################################

variable "secondary_region" {
  description = "Secondary AWS region for cross-region resources"
  type        = string
  default     = ""
  
  validation {
    condition = var.secondary_region == "" || contains([
      "us-east-1", "us-west-2"
    ], var.secondary_region)
    error_message = "Secondary region must be empty or one of the supported regions: us-east-1, us-west-2."
  }
}

variable "assume_role_arn" {
  description = "ARN of the IAM role to assume for AWS operations"
  type        = string
  default     = ""
  
  validation {
    condition = var.assume_role_arn == "" || can(regex("^arn:aws:iam::[0-9]{12}:role/.+", var.assume_role_arn))
    error_message = "Assume role ARN must be empty or a valid IAM role ARN."
  }
}

variable "assume_role_external_id" {
  description = "External ID for assume role operations"
  type        = string
  default     = ""
  sensitive   = true
}

variable "enable_global_resources" {
  description = "Whether to enable global resources (CloudFront, Route53, etc.)"
  type        = bool
  default     = false
}

###############################################################
# Backend Configuration Variables
###############################################################

variable "create_state_bucket" {
  description = "Whether to create the S3 bucket for Terraform state"
  type        = bool
  default     = false
}

variable "create_lock_table" {
  description = "Whether to create the DynamoDB table for state locking"
  type        = bool
  default     = false
}

###############################################################
# Network Configuration Variables
###############################################################

variable "vpc_name" {
  description = "Name of the VPC to use for resources"
  type        = string
  default     = "main-vpc"
  
  validation {
    condition     = length(var.vpc_name) > 0
    error_message = "VPC name cannot be empty."
  }
}

variable "enable_vpc_flow_logs" {
  description = "Whether to enable VPC Flow Logs"
  type        = bool
  default     = true
}

variable "vpc_flow_logs_retention" {
  description = "Retention period for VPC Flow Logs in days"
  type        = number
  default     = 30
  
  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.vpc_flow_logs_retention)
    error_message = "VPC Flow Logs retention must be a valid CloudWatch Logs retention value."
  }
}

###############################################################
# Security Configuration Variables
###############################################################

variable "enable_guardduty" {
  description = "Whether to enable AWS GuardDuty"
  type        = bool
  default     = true
}

variable "enable_config" {
  description = "Whether to enable AWS Config"
  type        = bool
  default     = true
}

variable "enable_cloudtrail" {
  description = "Whether to enable AWS CloudTrail"
  type        = bool
  default     = true
}

variable "cloudtrail_retention_days" {
  description = "Number of days to retain CloudTrail logs"
  type        = number
  default     = 90
  
  validation {
    condition     = var.cloudtrail_retention_days >= 1 && var.cloudtrail_retention_days <= 3653
    error_message = "CloudTrail retention must be between 1 and 3653 days."
  }
}

###############################################################
# Monitoring Configuration Variables
###############################################################

variable "enable_detailed_monitoring" {
  description = "Whether to enable detailed CloudWatch monitoring"
  type        = bool
  default     = true
}

variable "cloudwatch_log_retention_days" {
  description = "Default retention period for CloudWatch logs"
  type        = number
  default     = 30
  
  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.cloudwatch_log_retention_days)
    error_message = "CloudWatch log retention must be a valid retention value."
  }
}

variable "enable_xray_tracing" {
  description = "Whether to enable AWS X-Ray tracing"
  type        = bool
  default     = true
}

###############################################################
# Cost Management Variables
###############################################################

variable "cost_center" {
  description = "Cost center for billing and cost allocation"
  type        = string
  default     = ""
}

variable "budget_limit" {
  description = "Monthly budget limit in USD (0 = no budget)"
  type        = number
  default     = 0
  
  validation {
    condition     = var.budget_limit >= 0
    error_message = "Budget limit must be a positive number or zero."
  }
}

variable "enable_cost_anomaly_detection" {
  description = "Whether to enable AWS Cost Anomaly Detection"
  type        = bool
  default     = false
}

###############################################################
# Backup and Disaster Recovery Variables
###############################################################

variable "enable_backup" {
  description = "Whether to enable AWS Backup"
  type        = bool
  default     = true
}

variable "backup_retention_days" {
  description = "Number of days to retain backups"
  type        = number
  default     = 30
  
  validation {
    condition     = var.backup_retention_days >= 1 && var.backup_retention_days <= 36500
    error_message = "Backup retention must be between 1 and 36500 days."
  }
}

variable "enable_cross_region_backup" {
  description = "Whether to enable cross-region backup replication"
  type        = bool
  default     = false
}

###############################################################
# Compliance and Governance Variables
###############################################################

variable "compliance_framework" {
  description = "Compliance framework to follow (SOC2, HIPAA, PCI, etc.)"
  type        = string
  default     = ""
  
  validation {
    condition = var.compliance_framework == "" || contains([
      "SOC2", "HIPAA", "PCI", "FedRAMP", "ISO27001"
    ], var.compliance_framework)
    error_message = "Compliance framework must be empty or one of: SOC2, HIPAA, PCI, FedRAMP, ISO27001."
  }
}

variable "data_classification" {
  description = "Data classification level"
  type        = string
  default     = "internal"
  
  validation {
    condition = contains([
      "public", "internal", "confidential", "restricted"
    ], var.data_classification)
    error_message = "Data classification must be one of: public, internal, confidential, restricted."
  }
}

###############################################################
# Feature Flags
###############################################################

variable "feature_flags" {
  description = "Feature flags for enabling/disabling functionality"
  type = object({
    enable_waf                = optional(bool, false)
    enable_shield_advanced    = optional(bool, false)
    enable_secrets_manager    = optional(bool, true)
    enable_parameter_store    = optional(bool, true)
    enable_systems_manager    = optional(bool, true)
    enable_inspector         = optional(bool, false)
    enable_macie             = optional(bool, false)
    enable_security_hub      = optional(bool, false)
  })
  default = {}
}

# Shared Provider Configuration
# This file defines provider configurations that are common across all environments

terraform {
  required_version = ">= 1.5.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 3.1"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.2"
    }
    template = {
      source  = "hashicorp/template"
      version = "~> 2.2"
    }
  }
}

# Primary AWS Provider
provider "aws" {
  region = var.region

  # Default tags applied to all resources
  default_tags {
    tags = {
      Application  = var.application
      Environment  = var.environment
      Region       = var.region
      ManagedBy    = "terraform"
      Workspace    = terraform.workspace
      LaunchedBy   = var.launched_by
      LaunchedOn   = var.launched_on
      SlackContact = var.slack_contact
    }
  }

  # Assume role configuration (if needed)
  dynamic "assume_role" {
    for_each = var.assume_role_arn != "" ? [1] : []
    content {
      role_arn     = var.assume_role_arn
      session_name = "terraform-${var.environment}-${var.application}"
      external_id  = var.assume_role_external_id
    }
  }
}

# Secondary AWS Provider for cross-region resources (if needed)
provider "aws" {
  alias  = "secondary"
  region = var.secondary_region

  # Default tags for secondary region
  default_tags {
    tags = {
      Application  = var.application
      Environment  = var.environment
      Region       = var.secondary_region
      ManagedBy    = "terraform"
      Workspace    = terraform.workspace
      LaunchedBy   = var.launched_by
      LaunchedOn   = var.launched_on
      SlackContact = var.slack_contact
      Purpose      = "SecondaryRegion"
    }
  }

  # Assume role configuration for secondary region (if needed)
  dynamic "assume_role" {
    for_each = var.assume_role_arn != "" ? [1] : []
    content {
      role_arn     = var.assume_role_arn
      session_name = "terraform-${var.environment}-${var.application}-secondary"
      external_id  = var.assume_role_external_id
    }
  }
}

# Provider for US East 1 (for global resources like CloudFront, Route53)
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"

  # Default tags for global resources
  default_tags {
    tags = {
      Application  = var.application
      Environment  = var.environment
      Region       = "us-east-1"
      ManagedBy    = "terraform"
      Workspace    = terraform.workspace
      LaunchedBy   = var.launched_by
      LaunchedOn   = var.launched_on
      SlackContact = var.slack_contact
      Purpose      = "GlobalResources"
    }
  }

  # Assume role configuration for global resources (if needed)
  dynamic "assume_role" {
    for_each = var.assume_role_arn != "" ? [1] : []
    content {
      role_arn     = var.assume_role_arn
      session_name = "terraform-${var.environment}-${var.application}-global"
      external_id  = var.assume_role_external_id
    }
  }
}

# Random provider for generating random values
provider "random" {
  # No specific configuration needed
}

# Null provider for local-exec and other null resources
provider "null" {
  # No specific configuration needed
}

# Archive provider for creating ZIP files
provider "archive" {
  # No specific configuration needed
}

# Template provider for template rendering
provider "template" {
  # No specific configuration needed
}

# Local values for provider configuration
locals {
  # AWS provider configuration
  aws_config = {
    region                = var.region
    secondary_region      = var.secondary_region
    assume_role_arn      = var.assume_role_arn
    assume_role_external_id = var.assume_role_external_id
  }

  # Provider versions for reference
  provider_versions = {
    terraform = ">= 1.5.0"
    aws       = "~> 5.0"
    random    = "~> 3.1"
    null      = "~> 3.1"
    archive   = "~> 2.2"
    template  = "~> 2.2"
  }
}

# Output provider configuration for reference
output "provider_configuration" {
  description = "Provider configuration details"
  value = {
    primary_region   = var.region
    secondary_region = var.secondary_region
    workspace       = terraform.workspace
    provider_versions = local.provider_versions
  }
}

# Data sources for provider information
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}
data "aws_partition" "current" {}

# Output AWS account and region information
output "aws_account_info" {
  description = "AWS account and region information"
  value = {
    account_id = data.aws_caller_identity.current.account_id
    region     = data.aws_region.current.name
    partition  = data.aws_partition.current.partition
    dns_suffix = data.aws_partition.current.dns_suffix
  }
}

# Validation for required variables
locals {
  required_vars_validation = {
    region_valid = contains(["us-east-1", "us-west-2"], var.region)
    environment_valid = contains([
      "production", "nonprod", "qamain", "qarapid", "uat", "scratch", "homenet"
    ], var.environment)
  }
}

# Validation checks
resource "null_resource" "provider_validation" {
  count = alltrue(values(local.required_vars_validation)) ? 0 : 1

  provisioner "local-exec" {
    command = <<-EOT
      echo "ERROR: Provider configuration validation failed"
      echo "Region valid: ${local.required_vars_validation.region_valid}"
      echo "Environment valid: ${local.required_vars_validation.environment_valid}"
      exit 1
    EOT
  }
}

# Provider feature flags (for future use)
locals {
  provider_features = {
    enable_secondary_region = var.secondary_region != ""
    enable_assume_role     = var.assume_role_arn != ""
    enable_global_resources = var.enable_global_resources
  }
}

output "provider_features" {
  description = "Enabled provider features"
  value       = local.provider_features
}

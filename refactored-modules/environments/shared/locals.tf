# Shared Local Values
# This file defines local values that are computed and shared across all environments

locals {
  ###############################################################
  # Environment Configuration
  ###############################################################

  # Environment-specific configuration mapping
  environment_config = {
    production = {
      name                    = "production"
      short_name             = "prod"
      tier                   = "production"
      high_availability      = true
      multi_az              = true
      backup_enabled        = true
      monitoring_level      = "detailed"
      log_retention_days    = 90
      instance_size_modifier = "large"
      min_capacity_modifier  = 2
      max_capacity_modifier  = 10
      enable_deletion_protection = true
      enable_termination_protection = true
    }

    nonprod = {
      name                    = "nonprod"
      short_name             = "np"
      tier                   = "non-production"
      high_availability      = false
      multi_az              = false
      backup_enabled        = true
      monitoring_level      = "basic"
      log_retention_days    = 30
      instance_size_modifier = "medium"
      min_capacity_modifier  = 1
      max_capacity_modifier  = 3
      enable_deletion_protection = false
      enable_termination_protection = false
    }

    qamain = {
      name                    = "qamain"
      short_name             = "qam"
      tier                   = "testing"
      high_availability      = false
      multi_az              = false
      backup_enabled        = false
      monitoring_level      = "basic"
      log_retention_days    = 14
      instance_size_modifier = "small"
      min_capacity_modifier  = 1
      max_capacity_modifier  = 2
      enable_deletion_protection = false
      enable_termination_protection = false
    }

    qarapid = {
      name                    = "qarapid"
      short_name             = "qar"
      tier                   = "testing"
      high_availability      = false
      multi_az              = false
      backup_enabled        = false
      monitoring_level      = "basic"
      log_retention_days    = 7
      instance_size_modifier = "small"
      min_capacity_modifier  = 1
      max_capacity_modifier  = 2
      enable_deletion_protection = false
      enable_termination_protection = false
    }

    uat = {
      name                    = "uat"
      short_name             = "uat"
      tier                   = "testing"
      high_availability      = false
      multi_az              = false
      backup_enabled        = true
      monitoring_level      = "basic"
      log_retention_days    = 30
      instance_size_modifier = "medium"
      min_capacity_modifier  = 1
      max_capacity_modifier  = 3
      enable_deletion_protection = false
      enable_termination_protection = false
    }

    scratch = {
      name                    = "scratch"
      short_name             = "scr"
      tier                   = "development"
      high_availability      = false
      multi_az              = false
      backup_enabled        = false
      monitoring_level      = "basic"
      log_retention_days    = 3
      instance_size_modifier = "micro"
      min_capacity_modifier  = 1
      max_capacity_modifier  = 1
      enable_deletion_protection = false
      enable_termination_protection = false
    }

    homenet = {
      name                    = "homenet"
      short_name             = "hn"
      tier                   = "development"
      high_availability      = false
      multi_az              = false
      backup_enabled        = false
      monitoring_level      = "basic"
      log_retention_days    = 7
      instance_size_modifier = "small"
      min_capacity_modifier  = 1
      max_capacity_modifier  = 2
      enable_deletion_protection = false
      enable_termination_protection = false
    }
  }

  # Current environment configuration
  current_env = local.environment_config[var.environment]

  ###############################################################
  # Region Configuration
  ###############################################################

  # Region-specific configuration
  region_config = {
    "us-east-1" = {
      name           = "us-east-1"
      short_name     = "ue1"
      display_name   = "US East (N. Virginia)"
      az_count       = 6
      is_primary     = true
      supports_global = true
    }

    "us-west-2" = {
      name           = "us-west-2"
      short_name     = "uw2"
      display_name   = "US West (Oregon)"
      az_count       = 4
      is_primary     = false
      supports_global = false
    }
  }

  # Current region configuration
  current_region = local.region_config[var.region]

  ###############################################################
  # Common Tags
  ###############################################################

  # Base tags applied to all resources
  common_tags = {
    Application         = var.application
    ApplicationAbbrev   = var.application_abbreviated
    Environment         = var.environment
    EnvironmentTier     = local.current_env.tier
    Region              = var.region
    RegionAbbrev        = var.region_abbreviated
    Release             = var.build_number
    LaunchedBy          = var.launched_by
    LaunchedOn          = var.launched_on
    SlackContact        = var.slack_contact
    ManagedBy           = "terraform"
    Workspace           = terraform.workspace
    CostCenter          = var.cost_center
    DataClassification  = var.data_classification
    ComplianceFramework = var.compliance_framework
  }

  # Security-specific tags
  security_tags = merge(local.common_tags, {
    SecurityReview = "required"
    BackupRequired = local.current_env.backup_enabled ? "true" : "false"
    MonitoringLevel = local.current_env.monitoring_level
  })

  ###############################################################
  # Naming Conventions
  ###############################################################

  # Standard naming patterns
  naming = {
    # Resource name pattern: {type}-{region}-{app}-{service}-{env}-{build}
    resource_prefix = "${var.region_abbreviated}-${var.application_abbreviated}-${local.current_env.short_name}"

    # Full resource name with component
    resource_name = "${var.region_abbreviated}-${var.application_abbreviated}-${local.current_env.short_name}-${var.build_number}"

    # S3 bucket naming (must be globally unique)
    s3_prefix = "${var.application}-${var.environment}-${var.region_abbreviated}"

    # CloudWatch log group prefix
    log_group_prefix = "/aws/${var.application}/${var.environment}"

    # IAM role prefix
    iam_prefix = "${var.application}-${var.environment}"

    # Security group prefix
    sg_prefix = "sg-${var.region_abbreviated}-${var.application_abbreviated}-${local.current_env.short_name}"
  }

  ###############################################################
  # Network Configuration
  ###############################################################

  # Network CIDR blocks (example - adjust based on your network design)
  network_config = {
    vpc_cidr = {
      production = "10.0.0.0/16"
      nonprod    = "10.1.0.0/16"
      qamain     = "10.2.0.0/16"
      qarapid    = "10.3.0.0/16"
      uat        = "10.4.0.0/16"
      scratch    = "10.5.0.0/16"
      homenet    = "10.6.0.0/16"
    }

    # Trusted CIDR blocks
    trusted_cidrs = {
      homenet    = "204.11.136.0/32"
      ais_office = "107.1.95.66/32"
      remote_vpn = "108.252.235.16/32"
      nfs_range  = "10.0.0.0/8"
      ground_dc  = ["98.175.77.34/32", "204.193.152.32/32"]
    }
  }

  ###############################################################
  # Instance Sizing
  ###############################################################

  # Instance type mapping based on environment and size modifier
  instance_types = {
    micro = {
      web      = "t3.micro"
      app      = "t3.micro"
      database = "db.t3.micro"
      cache    = "cache.t3.micro"
    }
    small = {
      web      = "t3.small"
      app      = "t3.small"
      database = "db.t3.small"
      cache    = "cache.t3.small"
    }
    medium = {
      web      = "t3.medium"
      app      = "t3.medium"
      database = "db.r6g.large"
      cache    = "cache.r6g.large"
    }
    large = {
      web      = "t3.large"
      app      = "m5.large"
      database = "db.r6g.xlarge"
      cache    = "cache.r6g.xlarge"
    }
  }

  # Current instance types based on environment
  current_instance_types = local.instance_types[local.current_env.instance_size_modifier]

  ###############################################################
  # Security Configuration
  ###############################################################

  # Security settings based on environment tier
  security_config = {
    encryption_at_rest = local.current_env.tier == "production" ? true : var.data_classification != "public"
    encryption_in_transit = true
    enable_waf = local.current_env.tier == "production" ? true : var.feature_flags.enable_waf
    enable_shield = local.current_env.tier == "production" ? true : var.feature_flags.enable_shield_advanced
    require_mfa = local.current_env.tier == "production"
    session_timeout = local.current_env.tier == "production" ? 3600 : 7200
  }

  ###############################################################
  # Monitoring Configuration
  ###############################################################

  # Monitoring settings based on environment
  monitoring_config = {
    detailed_monitoring = local.current_env.monitoring_level == "detailed"
    log_retention_days = local.current_env.log_retention_days
    enable_xray = var.enable_xray_tracing && local.current_env.tier != "development"
    alarm_evaluation_periods = local.current_env.tier == "production" ? 2 : 3
    alarm_datapoints_to_alarm = local.current_env.tier == "production" ? 2 : 2
  }

  ###############################################################
  # Backup Configuration
  ###############################################################

  # Backup settings based on environment
  backup_config = {
    enabled = local.current_env.backup_enabled
    retention_days = local.current_env.tier == "production" ? var.backup_retention_days : 7
    cross_region = local.current_env.tier == "production" ? var.enable_cross_region_backup : false
    backup_window = "03:00-04:00"
    maintenance_window = "sun:04:00-sun:05:00"
  }

  ###############################################################
  # Feature Flags Resolution
  ###############################################################

  # Resolved feature flags based on environment and configuration
  resolved_features = {
    enable_waf = local.security_config.enable_waf
    enable_shield_advanced = local.security_config.enable_shield
    enable_secrets_manager = var.feature_flags.enable_secrets_manager
    enable_parameter_store = var.feature_flags.enable_parameter_store
    enable_systems_manager = var.feature_flags.enable_systems_manager
    enable_inspector = var.feature_flags.enable_inspector && local.current_env.tier == "production"
    enable_macie = var.feature_flags.enable_macie && local.current_env.tier == "production"
    enable_security_hub = var.feature_flags.enable_security_hub && local.current_env.tier == "production"
  }
}

###############################################################
# Outputs for Module Usage
###############################################################

output "environment_config" {
  description = "Environment-specific configuration"
  value       = local.current_env
}

output "region_config" {
  description = "Region-specific configuration"
  value       = local.current_region
}

output "common_tags" {
  description = "Common tags for all resources"
  value       = local.common_tags
}

output "security_tags" {
  description = "Security-specific tags"
  value       = local.security_tags
}

output "naming" {
  description = "Naming conventions"
  value       = local.naming
}

output "network_config" {
  description = "Network configuration"
  value       = local.network_config
}

output "current_instance_types" {
  description = "Instance types for current environment"
  value       = local.current_instance_types
}

output "security_config" {
  description = "Security configuration"
  value       = local.security_config
}

output "monitoring_config" {
  description = "Monitoring configuration"
  value       = local.monitoring_config
}

output "backup_config" {
  description = "Backup configuration"
  value       = local.backup_config
}

output "resolved_features" {
  description = "Resolved feature flags"
  value       = local.resolved_features
}

#!/bin/bash
# Environment Deployment Script
# This script deploys infrastructure for a specific environment using Terraform

set -e

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENVIRONMENTS_DIR="$(dirname "$SCRIPT_DIR")"
TEMPLATES_DIR="$ENVIRONMENTS_DIR/templates"
CONFIGS_DIR="$ENVIRONMENTS_DIR/configs"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 <environment> [options]"
    echo ""
    echo "Deploys infrastructure for the specified environment."
    echo ""
    echo "Available environments:"
    echo "  production  - Production environment"
    echo "  nonprod     - Non-production environment"
    echo "  qamain      - QA main environment"
    echo "  qarapid     - QA rapid environment"
    echo "  uat         - User acceptance testing environment"
    echo "  scratch     - Scratch/development environment"
    echo "  homenet     - HomeNet environment"
    echo ""
    echo "Options:"
    echo "  --auto-approve    Skip interactive approval of the plan"
    echo "  --plan-only       Only run terraform plan, don't apply"
    echo "  --destroy         Destroy the infrastructure instead of creating it"
    echo "  --target=RESOURCE Target specific resource for deployment"
    echo "  --var='key=value' Override specific variables"
    echo "  --help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 nonprod"
    echo "  $0 production --plan-only"
    echo "  $0 scratch --auto-approve"
    echo "  $0 production --target=module.database"
    echo ""
}

# Function to validate environment
validate_environment() {
    local env="$1"
    local valid_environments=("production" "nonprod" "qamain" "qarapid" "uat" "scratch" "homenet")
    
    for valid_env in "${valid_environments[@]}"; do
        if [[ "$env" == "$valid_env" ]]; then
            return 0
        fi
    done
    
    return 1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed or not in PATH"
        exit 1
    fi
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials not configured or invalid"
        exit 1
    fi
    
    local aws_account=$(aws sts get-caller-identity --query Account --output text)
    local aws_region=$(aws configure get region)
    print_status "AWS Account: $aws_account"
    print_status "AWS Region: $aws_region"
    
    print_success "Prerequisites check passed"
}

# Function to setup terraform workspace
setup_workspace() {
    local environment="$1"
    
    print_status "Setting up Terraform workspace for $environment..."
    
    cd "$TEMPLATES_DIR"
    
    # Check if workspace exists
    if ! terraform workspace list | grep -q "\\b$environment\\b"; then
        print_error "Workspace '$environment' does not exist"
        print_status "Please create the workspace first using:"
        print_status "$ENVIRONMENTS_DIR/workspaces/create-workspace.sh $environment"
        exit 1
    fi
    
    # Select workspace
    terraform workspace select "$environment"
    
    local current_workspace=$(terraform workspace show)
    if [[ "$current_workspace" != "$environment" ]]; then
        print_error "Failed to select workspace: $environment"
        exit 1
    fi
    
    print_success "Using workspace: $current_workspace"
}

# Function to run terraform plan
run_terraform_plan() {
    local environment="$1"
    local config_file="$2"
    local target="$3"
    local extra_vars="$4"
    local destroy="$5"
    
    print_status "Running Terraform plan for $environment..."
    
    cd "$TEMPLATES_DIR"
    
    # Build terraform plan command
    local plan_cmd="terraform plan"
    
    if [[ "$destroy" == "true" ]]; then
        plan_cmd="$plan_cmd -destroy"
        print_warning "Planning DESTRUCTION of infrastructure"
    fi
    
    plan_cmd="$plan_cmd -var-file=\"$config_file\""
    
    if [[ -n "$target" ]]; then
        plan_cmd="$plan_cmd -target=\"$target\""
        print_status "Targeting specific resource: $target"
    fi
    
    if [[ -n "$extra_vars" ]]; then
        plan_cmd="$plan_cmd $extra_vars"
        print_status "Using additional variables: $extra_vars"
    fi
    
    plan_cmd="$plan_cmd -out=\"$environment.tfplan\""
    
    print_status "Executing: $plan_cmd"
    
    # Run terraform plan
    if eval "$plan_cmd"; then
        print_success "Terraform plan completed successfully"
        return 0
    else
        print_error "Terraform plan failed"
        return 1
    fi
}

# Function to run terraform apply
run_terraform_apply() {
    local environment="$1"
    local auto_approve="$2"
    
    print_status "Running Terraform apply for $environment..."
    
    cd "$TEMPLATES_DIR"
    
    # Check if plan file exists
    if [[ ! -f "$environment.tfplan" ]]; then
        print_error "Plan file not found: $environment.tfplan"
        exit 1
    fi
    
    # Build terraform apply command
    local apply_cmd="terraform apply"
    
    if [[ "$auto_approve" == "true" ]]; then
        apply_cmd="$apply_cmd -auto-approve"
        print_warning "Auto-approving deployment (no interactive confirmation)"
    fi
    
    apply_cmd="$apply_cmd \"$environment.tfplan\""
    
    print_status "Executing: $apply_cmd"
    
    # Run terraform apply
    if eval "$apply_cmd"; then
        print_success "Terraform apply completed successfully"
        
        # Clean up plan file
        rm -f "$environment.tfplan"
        
        return 0
    else
        print_error "Terraform apply failed"
        return 1
    fi
}

# Function to show deployment summary
show_deployment_summary() {
    local environment="$1"
    
    print_status "Deployment summary for $environment:"
    
    cd "$TEMPLATES_DIR"
    
    # Show terraform outputs
    if terraform output &> /dev/null; then
        echo ""
        print_status "Terraform outputs:"
        terraform output
    fi
    
    echo ""
    print_success "Deployment completed successfully!"
    
    # Show useful next steps
    echo ""
    echo "Useful commands:"
    echo "  View outputs:     terraform output"
    echo "  Show state:       terraform show"
    echo "  List resources:   terraform state list"
    echo "  Refresh state:    terraform refresh -var-file=\"$CONFIGS_DIR/$environment.tfvars\""
    echo ""
}

# Function to confirm production deployment
confirm_production_deployment() {
    local environment="$1"
    local destroy="$2"
    
    if [[ "$environment" == "production" ]]; then
        echo ""
        if [[ "$destroy" == "true" ]]; then
            print_warning "⚠️  WARNING: You are about to DESTROY the PRODUCTION environment!"
            print_warning "This action is IRREVERSIBLE and will delete all production resources!"
        else
            print_warning "⚠️  WARNING: You are about to deploy to the PRODUCTION environment!"
            print_warning "This will affect live systems and users!"
        fi
        echo ""
        
        read -p "Are you absolutely sure you want to proceed? Type 'yes' to continue: " -r
        if [[ "$REPLY" != "yes" ]]; then
            print_status "Deployment cancelled by user"
            exit 0
        fi
        
        print_status "Production deployment confirmed"
    fi
}

# Main function
main() {
    # Parse command line arguments
    local environment=""
    local auto_approve="false"
    local plan_only="false"
    local destroy="false"
    local target=""
    local extra_vars=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --auto-approve)
                auto_approve="true"
                shift
                ;;
            --plan-only)
                plan_only="true"
                shift
                ;;
            --destroy)
                destroy="true"
                shift
                ;;
            --target=*)
                target="${1#*=}"
                shift
                ;;
            --var=*)
                extra_vars="$extra_vars -var=\"${1#*=}\""
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            -*)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$environment" ]]; then
                    environment="$1"
                else
                    print_error "Multiple environments specified"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # Check if environment is provided
    if [[ -z "$environment" ]]; then
        print_error "Environment not specified"
        show_usage
        exit 1
    fi
    
    # Validate environment
    if ! validate_environment "$environment"; then
        print_error "Invalid environment: $environment"
        show_usage
        exit 1
    fi
    
    # Check if config file exists
    local config_file="$CONFIGS_DIR/$environment.tfvars"
    if [[ ! -f "$config_file" ]]; then
        print_error "Configuration file not found: $config_file"
        exit 1
    fi
    
    print_status "Starting deployment for environment: $environment"
    print_status "Using configuration: $config_file"
    
    # Check prerequisites
    check_prerequisites
    
    # Confirm production deployment
    confirm_production_deployment "$environment" "$destroy"
    
    # Setup workspace
    setup_workspace "$environment"
    
    # Run terraform plan
    if ! run_terraform_plan "$environment" "$config_file" "$target" "$extra_vars" "$destroy"; then
        exit 1
    fi
    
    # If plan-only, exit here
    if [[ "$plan_only" == "true" ]]; then
        print_success "Plan-only mode: Terraform plan completed successfully"
        print_status "Plan saved to: $TEMPLATES_DIR/$environment.tfplan"
        exit 0
    fi
    
    # Run terraform apply
    if ! run_terraform_apply "$environment" "$auto_approve"; then
        exit 1
    fi
    
    # Show deployment summary
    show_deployment_summary "$environment"
}

# Run main function with all arguments
main "$@"

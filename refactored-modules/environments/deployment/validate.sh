#!/bin/bash
# Environment Validation Script
# This script validates Terraform configurations and infrastructure state

set -e

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENVIRONMENTS_DIR="$(dirname "$SCRIPT_DIR")"
TEMPLATES_DIR="$ENVIRONMENTS_DIR/templates"
CONFIGS_DIR="$ENVIRONMENTS_DIR/configs"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [environment] [options]"
    echo ""
    echo "Validates Terraform configurations and infrastructure state."
    echo ""
    echo "Arguments:"
    echo "  environment     Specific environment to validate (optional)"
    echo ""
    echo "Options:"
    echo "  --all           Validate all environments"
    echo "  --config-only   Only validate configuration files"
    echo "  --state-only    Only validate infrastructure state"
    echo "  --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Validate current workspace"
    echo "  $0 production         # Validate production environment"
    echo "  $0 --all              # Validate all environments"
    echo "  $0 nonprod --config-only"
    echo ""
}

# Function to validate environment
validate_environment() {
    local env="$1"
    local valid_environments=("production" "nonprod" "qamain" "qarapid" "uat" "scratch" "homenet")
    
    for valid_env in "${valid_environments[@]}"; do
        if [[ "$env" == "$valid_env" ]]; then
            return 0
        fi
    done
    
    return 1
}

# Function to validate terraform configuration
validate_terraform_config() {
    local environment="$1"
    
    print_status "Validating Terraform configuration for $environment..."
    
    cd "$TEMPLATES_DIR"
    
    # Check if configuration file exists
    local config_file="$CONFIGS_DIR/$environment.tfvars"
    if [[ ! -f "$config_file" ]]; then
        print_error "Configuration file not found: $config_file"
        return 1
    fi
    
    # Validate terraform syntax
    if terraform validate; then
        print_success "Terraform syntax validation passed"
    else
        print_error "Terraform syntax validation failed"
        return 1
    fi
    
    # Validate configuration with terraform plan
    print_status "Running terraform plan to validate configuration..."
    
    # Select workspace if it exists
    if terraform workspace list | grep -q "\\b$environment\\b"; then
        terraform workspace select "$environment"
    else
        print_warning "Workspace '$environment' does not exist, using default"
    fi
    
    # Run plan with validation
    if terraform plan -var-file="$config_file" -detailed-exitcode > /dev/null; then
        print_success "Configuration validation passed"
        return 0
    else
        local exit_code=$?
        if [[ $exit_code -eq 2 ]]; then
            print_warning "Configuration is valid but changes are planned"
            return 0
        else
            print_error "Configuration validation failed"
            return 1
        fi
    fi
}

# Function to validate infrastructure state
validate_infrastructure_state() {
    local environment="$1"
    
    print_status "Validating infrastructure state for $environment..."
    
    cd "$TEMPLATES_DIR"
    
    # Check if workspace exists
    if ! terraform workspace list | grep -q "\\b$environment\\b"; then
        print_warning "Workspace '$environment' does not exist"
        return 1
    fi
    
    # Select workspace
    terraform workspace select "$environment"
    
    # Check if state exists
    if ! terraform state list &> /dev/null; then
        print_warning "No infrastructure state found for $environment"
        return 1
    fi
    
    # Validate state consistency
    print_status "Checking state consistency..."
    
    local config_file="$CONFIGS_DIR/$environment.tfvars"
    if terraform plan -var-file="$config_file" -detailed-exitcode > /dev/null; then
        print_success "Infrastructure state is consistent with configuration"
        return 0
    else
        local exit_code=$?
        if [[ $exit_code -eq 2 ]]; then
            print_warning "Infrastructure state differs from configuration (drift detected)"
            
            # Show drift details
            print_status "Drift details:"
            terraform plan -var-file="$config_file" -no-color | head -20
            echo "..."
            
            return 1
        else
            print_error "State validation failed"
            return 1
        fi
    fi
}

# Function to validate configuration file syntax
validate_config_file() {
    local config_file="$1"
    local environment="$2"
    
    print_status "Validating configuration file: $config_file"
    
    # Check if file exists
    if [[ ! -f "$config_file" ]]; then
        print_error "Configuration file not found: $config_file"
        return 1
    fi
    
    # Check file syntax (basic HCL validation)
    if terraform fmt -check=true -diff=true "$config_file" > /dev/null 2>&1; then
        print_success "Configuration file syntax is valid"
    else
        print_warning "Configuration file formatting issues detected"
        terraform fmt -diff=true "$config_file"
    fi
    
    # Check for required variables
    local required_vars=("application" "environment" "region" "build_number")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var[[:space:]]*=" "$config_file"; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_error "Missing required variables in $config_file:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        return 1
    fi
    
    print_success "Required variables check passed"
    return 0
}

# Function to validate all environments
validate_all_environments() {
    local config_only="$1"
    local state_only="$2"
    
    print_status "Validating all environments..."
    
    local environments=("production" "nonprod" "qamain" "qarapid" "uat" "scratch" "homenet")
    local validation_results=()
    
    for env in "${environments[@]}"; do
        echo ""
        print_status "=== Validating $env ==="
        
        local config_result=0
        local state_result=0
        
        # Validate configuration
        if [[ "$state_only" != "true" ]]; then
            if validate_config_file "$CONFIGS_DIR/$env.tfvars" "$env"; then
                if validate_terraform_config "$env"; then
                    config_result=0
                else
                    config_result=1
                fi
            else
                config_result=1
            fi
        fi
        
        # Validate state
        if [[ "$config_only" != "true" ]]; then
            if validate_infrastructure_state "$env"; then
                state_result=0
            else
                state_result=1
            fi
        fi
        
        # Record results
        if [[ $config_result -eq 0 && $state_result -eq 0 ]]; then
            validation_results+=("$env:PASS")
            print_success "$env validation passed"
        else
            validation_results+=("$env:FAIL")
            print_error "$env validation failed"
        fi
    done
    
    # Summary
    echo ""
    print_status "=== Validation Summary ==="
    
    local passed=0
    local failed=0
    
    for result in "${validation_results[@]}"; do
        local env="${result%:*}"
        local status="${result#*:}"
        
        if [[ "$status" == "PASS" ]]; then
            echo -e "  ${GREEN}✓${NC} $env"
            ((passed++))
        else
            echo -e "  ${RED}✗${NC} $env"
            ((failed++))
        fi
    done
    
    echo ""
    print_status "Passed: $passed, Failed: $failed"
    
    if [[ $failed -eq 0 ]]; then
        print_success "All environment validations passed!"
        return 0
    else
        print_error "Some environment validations failed"
        return 1
    fi
}

# Main function
main() {
    # Parse command line arguments
    local environment=""
    local validate_all="false"
    local config_only="false"
    local state_only="false"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --all)
                validate_all="true"
                shift
                ;;
            --config-only)
                config_only="true"
                shift
                ;;
            --state-only)
                state_only="true"
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            -*)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$environment" ]]; then
                    environment="$1"
                else
                    print_error "Multiple environments specified"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # Check for conflicting options
    if [[ "$config_only" == "true" && "$state_only" == "true" ]]; then
        print_error "Cannot specify both --config-only and --state-only"
        exit 1
    fi
    
    # Validate all environments if requested
    if [[ "$validate_all" == "true" ]]; then
        if [[ -n "$environment" ]]; then
            print_error "Cannot specify environment with --all option"
            exit 1
        fi
        
        validate_all_environments "$config_only" "$state_only"
        exit $?
    fi
    
    # If no environment specified, use current workspace
    if [[ -z "$environment" ]]; then
        cd "$TEMPLATES_DIR"
        if command -v terraform &> /dev/null && [[ -f ".terraform/environment" ]]; then
            environment=$(terraform workspace show)
            print_status "Using current workspace: $environment"
        else
            print_error "No environment specified and no current workspace found"
            show_usage
            exit 1
        fi
    fi
    
    # Validate environment
    if ! validate_environment "$environment"; then
        print_error "Invalid environment: $environment"
        show_usage
        exit 1
    fi
    
    print_status "Validating environment: $environment"
    
    local validation_passed=true
    
    # Validate configuration
    if [[ "$state_only" != "true" ]]; then
        if ! validate_config_file "$CONFIGS_DIR/$environment.tfvars" "$environment"; then
            validation_passed=false
        fi
        
        if ! validate_terraform_config "$environment"; then
            validation_passed=false
        fi
    fi
    
    # Validate state
    if [[ "$config_only" != "true" ]]; then
        if ! validate_infrastructure_state "$environment"; then
            validation_passed=false
        fi
    fi
    
    # Final result
    if [[ "$validation_passed" == "true" ]]; then
        print_success "Environment validation passed!"
        exit 0
    else
        print_error "Environment validation failed!"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"

#!/bin/bash
# Create Terraform Workspace Script
# This script creates a new Terraform workspace for environment deployment

set -e

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENVIRONMENTS_DIR="$(dirname "$SCRIPT_DIR")"
TEMPLATES_DIR="$ENVIRONMENTS_DIR/templates"
CONFIGS_DIR="$ENVIRONMENTS_DIR/configs"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 <environment>"
    echo ""
    echo "Creates a new Terraform workspace for the specified environment."
    echo ""
    echo "Available environments:"
    echo "  production  - Production environment"
    echo "  nonprod     - Non-production environment"
    echo "  qamain      - QA main environment"
    echo "  qarapid     - QA rapid environment"
    echo "  uat         - User acceptance testing environment"
    echo "  scratch     - Scratch/development environment"
    echo "  homenet     - HomeNet environment"
    echo ""
    echo "Examples:"
    echo "  $0 production"
    echo "  $0 nonprod"
    echo ""
}

# Function to validate environment
validate_environment() {
    local env="$1"
    local valid_environments=("production" "nonprod" "qamain" "qarapid" "uat" "scratch" "homenet")
    
    for valid_env in "${valid_environments[@]}"; do
        if [[ "$env" == "$valid_env" ]]; then
            return 0
        fi
    done
    
    return 1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed or not in PATH"
        exit 1
    fi
    
    # Check terraform version
    local tf_version=$(terraform version -json | jq -r '.terraform_version')
    print_status "Terraform version: $tf_version"
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed or not in PATH"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials not configured or invalid"
        exit 1
    fi
    
    local aws_account=$(aws sts get-caller-identity --query Account --output text)
    local aws_region=$(aws configure get region)
    print_status "AWS Account: $aws_account"
    print_status "AWS Region: $aws_region"
    
    # Check if templates directory exists
    if [[ ! -d "$TEMPLATES_DIR" ]]; then
        print_error "Templates directory not found: $TEMPLATES_DIR"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to initialize terraform backend
initialize_backend() {
    local environment="$1"
    local region="$2"
    local region_abbrev="$3"
    
    print_status "Initializing Terraform backend for $environment..."
    
    # Create backend configuration
    local backend_config_file="$TEMPLATES_DIR/backend-$environment.hcl"
    
    cat > "$backend_config_file" << EOF
bucket         = "ais-terraform-state-${region_abbrev}-${environment}"
key            = "infrastructure/${environment}/terraform.tfstate"
region         = "${region}"
dynamodb_table = "ais-terraform-locks-${region_abbrev}"
encrypt        = true
EOF
    
    print_status "Created backend configuration: $backend_config_file"
    
    # Initialize terraform with backend configuration
    cd "$TEMPLATES_DIR"
    
    if terraform init -backend-config="$backend_config_file" -reconfigure; then
        print_success "Terraform backend initialized successfully"
    else
        print_error "Failed to initialize Terraform backend"
        exit 1
    fi
    
    # Clean up backend config file
    rm -f "$backend_config_file"
}

# Function to create workspace
create_workspace() {
    local environment="$1"
    
    print_status "Creating Terraform workspace: $environment"
    
    cd "$TEMPLATES_DIR"
    
    # Check if workspace already exists
    if terraform workspace list | grep -q "\\b$environment\\b"; then
        print_warning "Workspace '$environment' already exists"
        
        # Ask user if they want to switch to existing workspace
        read -p "Do you want to switch to the existing workspace? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            terraform workspace select "$environment"
            print_success "Switched to existing workspace: $environment"
        else
            print_status "Keeping current workspace"
        fi
    else
        # Create new workspace
        if terraform workspace new "$environment"; then
            print_success "Created new workspace: $environment"
        else
            print_error "Failed to create workspace: $environment"
            exit 1
        fi
    fi
    
    # Verify current workspace
    local current_workspace=$(terraform workspace show)
    print_status "Current workspace: $current_workspace"
}

# Function to validate configuration
validate_configuration() {
    local environment="$1"
    
    print_status "Validating Terraform configuration for $environment..."
    
    cd "$TEMPLATES_DIR"
    
    # Check if environment config file exists
    local config_file="$CONFIGS_DIR/$environment.tfvars"
    if [[ ! -f "$config_file" ]]; then
        print_error "Configuration file not found: $config_file"
        exit 1
    fi
    
    print_status "Using configuration file: $config_file"
    
    # Validate terraform configuration
    if terraform validate; then
        print_success "Terraform configuration is valid"
    else
        print_error "Terraform configuration validation failed"
        exit 1
    fi
    
    # Plan with the environment configuration
    print_status "Running terraform plan to validate configuration..."
    if terraform plan -var-file="$config_file" -out="$environment.tfplan"; then
        print_success "Terraform plan completed successfully"
        
        # Show plan summary
        terraform show -no-color "$environment.tfplan" | head -20
        echo "..."
        echo "(Plan output truncated. Full plan saved to $environment.tfplan)"
        
        # Clean up plan file
        rm -f "$environment.tfplan"
    else
        print_error "Terraform plan failed"
        exit 1
    fi
}

# Function to show next steps
show_next_steps() {
    local environment="$1"
    
    echo ""
    print_success "Workspace '$environment' has been created and validated successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Review the configuration file: $CONFIGS_DIR/$environment.tfvars"
    echo "2. Deploy the infrastructure:"
    echo "   cd $TEMPLATES_DIR"
    echo "   terraform workspace select $environment"
    echo "   terraform apply -var-file=\"$CONFIGS_DIR/$environment.tfvars\""
    echo ""
    echo "Or use the deployment script:"
    echo "   $ENVIRONMENTS_DIR/deployment/deploy.sh $environment"
    echo ""
}

# Main function
main() {
    # Check if environment argument is provided
    if [[ $# -ne 1 ]]; then
        show_usage
        exit 1
    fi
    
    local environment="$1"
    
    # Validate environment
    if ! validate_environment "$environment"; then
        print_error "Invalid environment: $environment"
        show_usage
        exit 1
    fi
    
    print_status "Creating workspace for environment: $environment"
    
    # Check prerequisites
    check_prerequisites
    
    # Get region information from config file
    local config_file="$CONFIGS_DIR/$environment.tfvars"
    if [[ ! -f "$config_file" ]]; then
        print_error "Configuration file not found: $config_file"
        exit 1
    fi
    
    local region=$(grep '^region[[:space:]]*=' "$config_file" | sed 's/.*=[[:space:]]*"\([^"]*\)".*/\1/')
    local region_abbrev=$(grep '^region_abbreviated[[:space:]]*=' "$config_file" | sed 's/.*=[[:space:]]*"\([^"]*\)".*/\1/')
    
    if [[ -z "$region" || -z "$region_abbrev" ]]; then
        print_error "Could not extract region information from config file"
        exit 1
    fi
    
    print_status "Region: $region ($region_abbrev)"
    
    # Initialize backend
    initialize_backend "$environment" "$region" "$region_abbrev"
    
    # Create workspace
    create_workspace "$environment"
    
    # Validate configuration
    validate_configuration "$environment"
    
    # Show next steps
    show_next_steps "$environment"
}

# Run main function with all arguments
main "$@"

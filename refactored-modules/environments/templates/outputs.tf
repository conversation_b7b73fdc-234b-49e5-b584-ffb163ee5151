# Environment Template Outputs
# This file defines outputs for the complete environment deployment

###############################################################
# Environment Information
###############################################################

output "environment_info" {
  description = "Environment configuration information"
  value = {
    environment        = var.environment
    region            = var.region
    application       = var.application
    build_number      = var.build_number
    workspace         = terraform.workspace
    launched_by       = var.launched_by
    launched_on       = var.launched_on
    tier              = module.shared_config.environment_config.tier
    high_availability = module.shared_config.environment_config.high_availability
  }
}

###############################################################
# Networking Information
###############################################################

output "networking_info" {
  description = "Networking configuration information"
  value = {
    vpc_id              = module.networking.vpc_id
    vpc_cidr            = module.networking.vpc_cidr
    public_subnet_ids   = module.networking.public_subnet_ids
    private_subnet_ids  = module.networking.private_subnet_ids
    availability_zones  = module.networking.availability_zones
    nat_gateway_ids     = module.networking.nat_gateway_ids
    internet_gateway_id = module.networking.internet_gateway_id
  }
}

###############################################################
# Security Information
###############################################################

output "security_info" {
  description = "Security configuration information"
  value = {
    security_group_ids = module.security_groups.security_group_ids
    security_group_arns = module.security_groups.security_group_arns
    encryption_enabled = module.shared_config.security_config.encryption_at_rest
    waf_enabled       = module.shared_config.resolved_features.enable_waf
    shield_enabled    = module.shared_config.resolved_features.enable_shield_advanced
  }
}

###############################################################
# Database Information
###############################################################

output "database_info" {
  description = "Database configuration information"
  value = var.enable_database ? {
    cluster_endpoint          = module.database[0].cluster_endpoint
    cluster_reader_endpoint   = module.database[0].cluster_reader_endpoint
    cluster_arn              = module.database[0].cluster_arn
    cluster_id               = module.database[0].cluster_id
    cluster_port             = module.database[0].cluster_port
    cluster_database_name    = module.database[0].cluster_database_name
    cluster_master_username  = module.database[0].cluster_master_username
    cluster_engine_version   = module.database[0].cluster_engine_version
    cluster_instances        = module.database[0].cluster_instances
    security_group_id        = module.database[0].security_group_id
    subnet_group_name        = module.database[0].subnet_group_name
    parameter_group_name     = module.database[0].parameter_group_name
    monitoring_role_arn      = module.database[0].monitoring_role_arn
  } : null
  sensitive = true
}

###############################################################
# Load Balancer Information
###############################################################

output "load_balancer_info" {
  description = "Load balancer configuration information"
  value = var.enable_load_balancer ? {
    arn                    = module.load_balancer[0].arn
    dns_name              = module.load_balancer[0].dns_name
    zone_id               = module.load_balancer[0].zone_id
    target_group_arns     = module.load_balancer[0].target_group_arns
    target_group_names    = module.load_balancer[0].target_group_names
    listener_arns         = module.load_balancer[0].listener_arns
    security_group_id     = module.load_balancer[0].security_group_id
    access_logs_bucket    = module.load_balancer[0].access_logs_bucket
  } : null
}

###############################################################
# Web Servers Information
###############################################################

output "web_servers_info" {
  description = "Web servers configuration information"
  value = var.enable_web_servers ? {
    autoscaling_group_name     = module.web_servers[0].autoscaling_group_name
    autoscaling_group_arn      = module.web_servers[0].autoscaling_group_arn
    launch_template_id         = module.web_servers[0].launch_template_id
    launch_template_version    = module.web_servers[0].launch_template_version
    iam_role_name             = module.web_servers[0].iam_role_name
    iam_role_arn              = module.web_servers[0].iam_role_arn
    iam_instance_profile_name = module.web_servers[0].iam_instance_profile_name
    security_group_id         = module.web_servers[0].security_group_id
    cloudwatch_log_groups     = module.web_servers[0].cloudwatch_log_groups
    scaling_policies          = module.web_servers[0].scaling_policies
    cloudwatch_alarms         = module.web_servers[0].cloudwatch_alarms
  } : null
}

###############################################################
# Lambda Functions Information
###############################################################

output "lambda_functions_info" {
  description = "Lambda functions configuration information"
  value = {
    for name, config in var.lambda_functions_config :
    name => {
      function_name                = module.lambda_functions[name].lambda_function_name
      function_arn                = module.lambda_functions[name].lambda_function_arn
      function_qualified_arn      = module.lambda_functions[name].lambda_function_qualified_arn
      function_version            = module.lambda_functions[name].lambda_function_version
      function_invoke_arn         = module.lambda_functions[name].lambda_function_invoke_arn
      function_last_modified      = module.lambda_functions[name].lambda_function_last_modified
      function_source_code_hash   = module.lambda_functions[name].lambda_function_source_code_hash
      function_source_code_size   = module.lambda_functions[name].lambda_function_source_code_size
      role_arn                    = module.lambda_functions[name].lambda_role_arn
      role_name                   = module.lambda_functions[name].lambda_role_name
      cloudwatch_log_group_arn    = module.lambda_functions[name].lambda_cloudwatch_log_group_arn
      cloudwatch_log_group_name   = module.lambda_functions[name].lambda_cloudwatch_log_group_name
      monitoring_info             = module.lambda_functions[name].monitoring_info
      security_info               = module.lambda_functions[name].security_info
    }
  }
}

###############################################################
# Application URLs and Endpoints
###############################################################

output "application_endpoints" {
  description = "Application endpoints and URLs"
  value = {
    # Primary application URL (if load balancer is enabled)
    application_url = var.enable_load_balancer ? "https://${module.load_balancer[0].dns_name}" : null
    
    # Load balancer endpoint
    load_balancer_endpoint = var.enable_load_balancer ? {
      dns_name = module.load_balancer[0].dns_name
      zone_id  = module.load_balancer[0].zone_id
    } : null
    
    # Database endpoint (sensitive)
    database_endpoint = var.enable_database ? module.database[0].cluster_endpoint : null
    
    # Lambda function invoke ARNs
    lambda_invoke_arns = {
      for name, config in var.lambda_functions_config :
      name => module.lambda_functions[name].lambda_function_invoke_arn
    }
  }
  sensitive = true
}

###############################################################
# Monitoring and Observability
###############################################################

output "monitoring_info" {
  description = "Monitoring and observability information"
  value = {
    # CloudWatch log groups
    log_groups = merge(
      var.enable_web_servers ? { web_servers = module.web_servers[0].cloudwatch_log_groups } : {},
      var.enable_database ? { database = module.database[0].cloudwatch_log_groups } : {},
      {
        lambda_functions = {
          for name, config in var.lambda_functions_config :
          name => module.lambda_functions[name].lambda_cloudwatch_log_group_name
        }
      }
    )
    
    # CloudWatch alarms
    alarms = merge(
      var.enable_web_servers ? { web_servers = module.web_servers[0].cloudwatch_alarms } : {},
      var.enable_database ? { database = module.database[0].cloudwatch_alarms } : {},
      {
        lambda_functions = {
          for name, config in var.lambda_functions_config :
          name => module.lambda_functions[name].cloudwatch_alarms
        }
      }
    )
    
    # X-Ray tracing
    xray_enabled = module.shared_config.monitoring_config.enable_xray
    
    # Monitoring level
    monitoring_level = module.shared_config.monitoring_config.detailed_monitoring ? "detailed" : "basic"
  }
}

###############################################################
# Cost and Resource Information
###############################################################

output "cost_info" {
  description = "Cost and resource allocation information"
  value = {
    # Environment tier affects cost
    environment_tier = module.shared_config.environment_config.tier
    
    # Instance types being used
    instance_types = module.shared_config.current_instance_types
    
    # Resource counts
    resource_counts = {
      database_instances = var.enable_database ? length(module.database[0].cluster_instances) : 0
      web_server_capacity = var.enable_web_servers ? {
        min_size         = module.web_servers[0].autoscaling_group_min_size
        max_size         = module.web_servers[0].autoscaling_group_max_size
        desired_capacity = module.web_servers[0].autoscaling_group_desired_capacity
      } : null
      lambda_functions = length(var.lambda_functions_config)
    }
    
    # High availability features (affect cost)
    high_availability = {
      enabled           = module.shared_config.environment_config.high_availability
      multi_az         = module.shared_config.environment_config.multi_az
      backup_enabled   = module.shared_config.backup_config.enabled
      cross_region_backup = module.shared_config.backup_config.cross_region
    }
    
    # Cost optimization recommendations
    cost_optimization = {
      environment_appropriate_sizing = true
      backup_retention_optimized = module.shared_config.backup_config.retention_days
      monitoring_level_appropriate = module.shared_config.monitoring_config.detailed_monitoring
    }
  }
}

###############################################################
# Deployment Information
###############################################################

output "deployment_info" {
  description = "Deployment configuration and status"
  value = {
    # Deployment metadata
    workspace        = terraform.workspace
    build_number     = var.build_number
    launched_by      = var.launched_by
    launched_on      = var.launched_on
    
    # Component status
    components_deployed = {
      networking     = true
      security_groups = true
      database       = var.enable_database
      load_balancer  = var.enable_load_balancer
      web_servers    = var.enable_web_servers
      lambda_functions = length(var.lambda_functions_config) > 0
    }
    
    # Configuration summary
    configuration_summary = {
      environment_config = module.shared_config.environment_config
      security_features = module.shared_config.resolved_features
      backup_config     = module.shared_config.backup_config
      monitoring_config = module.shared_config.monitoring_config
    }
    
    # Backend configuration
    backend_info = module.shared_config.backend_configuration
  }
}

# Environment Template - Main Infrastructure
# This template defines the complete infrastructure for any environment
# using the refactored modules with environment-specific configurations

###############################################################
# Shared Configuration Import
###############################################################

# Import shared backend configuration
terraform {
  required_version = ">= 1.5.0"

  backend "s3" {
    # Backend configuration will be provided via backend config file
    # or command line arguments during terraform init
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
    null = {
      source  = "hashicorp/null"
      version = "~> 3.1"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.2"
    }
  }
}

# Import shared provider configuration
provider "aws" {
  region = var.region

  default_tags {
    tags = local.common_tags
  }
}

# Import shared configuration files
module "shared_config" {
  source = "../shared"

  # Pass through all required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  environment            = var.environment
  region                 = var.region
  region_abbreviated     = var.region_abbreviated
  build_number           = var.build_number
  launched_by            = var.launched_by
  launched_on            = var.launched_on
  slack_contact          = var.slack_contact

  # Additional shared configuration
  vpc_name               = var.vpc_name
  cost_center           = var.cost_center
  data_classification   = var.data_classification
  compliance_framework  = var.compliance_framework
  feature_flags         = var.feature_flags
}

# Import shared locals and variables
locals {
  # Import shared locals from module
  environment_config = module.shared_config.environment_config
  region_config     = module.shared_config.region_config
  common_tags       = module.shared_config.common_tags
  naming           = module.shared_config.naming
  security_config  = module.shared_config.security_config
  monitoring_config = module.shared_config.monitoring_config
  backup_config    = module.shared_config.backup_config
  network_config   = module.shared_config.network_config
  current_instance_types = module.shared_config.current_instance_types
  resolved_features = module.shared_config.resolved_features
}

###############################################################
# Foundation Layer - Networking and Security
###############################################################

# Networking Data Sources
module "networking" {
  source = "../../refactored-modules/foundation/networking/data-sources"

  # Required variables
  application = var.application
  environment = var.environment
  region      = var.region
  vpc_name    = var.vpc_name

  # Pass through all required variables
  application_abbreviated = var.application_abbreviated
  service                = "networking"
  component              = "data-sources"
  region_abbreviated     = var.region_abbreviated
  build_number          = var.build_number
  launched_by           = var.launched_by
  launched_on           = var.launched_on
  slack_contact         = var.slack_contact
}

# Security Groups
module "security_groups" {
  source = "../../refactored-modules/foundation/security/security-groups"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                = "security"
  component              = "security-groups"
  environment            = var.environment
  region                 = var.region
  region_abbreviated     = var.region_abbreviated
  build_number           = var.build_number
  launched_by            = var.launched_by
  launched_on            = var.launched_on
  slack_contact          = var.slack_contact

  # Network configuration
  vpc_id          = module.networking.vpc_id
  homenet_cidr    = local.network_config.trusted_cidrs.homenet
  ais_cidr        = local.network_config.trusted_cidrs.ais_office
  remote_cidr     = local.network_config.trusted_cidrs.remote_vpn
  nfs_cidr        = local.network_config.trusted_cidrs.nfs_range
  ground_dc_cidrs = local.network_config.trusted_cidrs.ground_dc

  # Environment-specific security groups configuration
  security_groups = var.security_groups_config
}

###############################################################
# Storage Layer - Databases
###############################################################

# Aurora MySQL Database (if enabled)
module "database" {
  count  = var.enable_database ? 1 : 0
  source = "../../refactored-modules/storage/databases/aurora-mysql"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                = "database"
  component              = var.database_config.component_name
  environment            = var.environment
  region                 = var.region
  region_abbreviated     = var.region_abbreviated
  build_number           = var.build_number
  launched_by            = var.launched_by
  launched_on            = var.launched_on
  slack_contact          = var.slack_contact

  # Database configuration
  database_name   = var.database_config.database_name
  master_username = var.database_config.master_username

  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["database"]]

  # Instance configuration based on environment
  instance_class = local.current_instance_types.database
  replica_count  = local.environment_config.high_availability ? var.database_config.replica_count : 0

  # Security configuration
  storage_encrypted               = local.security_config.encryption_at_rest
  deletion_protection            = local.environment_config.enable_deletion_protection
  manage_master_user_password    = true
  kms_key_id                     = var.database_config.kms_key_id

  # Monitoring
  performance_insights_enabled    = local.monitoring_config.detailed_monitoring
  monitoring_interval            = local.monitoring_config.detailed_monitoring ? 60 : 0
  enabled_cloudwatch_logs_exports = var.database_config.enabled_cloudwatch_logs_exports

  # Backup configuration
  backup_retention_period = local.backup_config.retention_days
  backup_window          = local.backup_config.backup_window
  maintenance_window     = local.backup_config.maintenance_window
  skip_final_snapshot    = !local.backup_config.enabled
}

###############################################################
# Networking Layer - Load Balancing
###############################################################

# Application Load Balancer (if enabled)
module "load_balancer" {
  count  = var.enable_load_balancer ? 1 : 0
  source = "../../refactored-modules/networking/load-balancer/alb"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                = "web"
  component              = var.load_balancer_config.component_name
  environment            = var.environment
  region                 = var.region
  region_abbreviated     = var.region_abbreviated
  build_number           = var.build_number
  launched_by            = var.launched_by
  launched_on            = var.launched_on
  slack_contact          = var.slack_contact

  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.public_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["load-balancer"]]

  # Load balancer configuration
  internal                   = var.load_balancer_config.internal
  enable_deletion_protection = local.environment_config.enable_deletion_protection
  enable_access_logs        = local.monitoring_config.detailed_monitoring
  certificate_arn           = var.load_balancer_config.certificate_arn

  # Target groups and listeners from environment configuration
  target_groups = var.load_balancer_config.target_groups
  listeners     = var.load_balancer_config.listeners

  # WAF integration (if enabled)
  enable_waf = local.resolved_features.enable_waf
  waf_config = var.load_balancer_config.waf_config
}

###############################################################
# Compute Layer - EC2 and Lambda
###############################################################

# EC2 Auto Scaling Group (if enabled)
module "web_servers" {
  count  = var.enable_web_servers ? 1 : 0
  source = "../../refactored-modules/compute/ec2-asg"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                = "web"
  component              = var.web_servers_config.component_name
  environment            = var.environment
  region                 = var.region
  region_abbreviated     = var.region_abbreviated
  build_number           = var.build_number
  launched_by            = var.launched_by
  launched_on            = var.launched_on
  slack_contact          = var.slack_contact

  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["web-server"]]

  # Instance configuration based on environment
  instance_type = local.current_instance_types.web
  key_name      = var.web_servers_config.key_name

  # Auto Scaling configuration with environment-based sizing
  min_size         = local.environment_config.min_capacity_modifier * var.web_servers_config.base_capacity
  max_size         = local.environment_config.max_capacity_modifier * var.web_servers_config.base_capacity
  desired_capacity = local.environment_config.min_capacity_modifier * var.web_servers_config.base_capacity

  # Load balancer integration
  target_group_arns = var.enable_load_balancer ? [module.load_balancer[0].target_group_arns[var.web_servers_config.target_group_name]] : []
  health_check_type = var.enable_load_balancer ? "ELB" : "EC2"

  # Storage configuration
  root_volume_size      = var.web_servers_config.root_volume_size
  root_volume_type      = "gp3"
  root_volume_encrypted = local.security_config.encryption_at_rest

  # Monitoring
  enable_cloudwatch_agent = local.monitoring_config.detailed_monitoring
  enable_ssm_agent        = local.resolved_features.enable_systems_manager
  enable_monitoring       = local.monitoring_config.detailed_monitoring

  # Scaling policies
  enable_scaling_policies         = true
  cpu_utilization_high_threshold  = var.web_servers_config.cpu_high_threshold
  cpu_utilization_low_threshold   = var.web_servers_config.cpu_low_threshold

  # Custom user data
  user_data_script = var.web_servers_config.user_data_script

  depends_on = [module.database, module.load_balancer]
}

# Lambda Functions (if enabled)
module "lambda_functions" {
  for_each = var.lambda_functions_config
  source   = "../../refactored-modules/compute/lambda"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                = each.value.service
  component              = each.value.component
  environment            = var.environment
  region                 = var.region
  region_abbreviated     = var.region_abbreviated
  build_number           = var.build_number
  launched_by            = var.launched_by
  launched_on            = var.launched_on
  slack_contact          = var.slack_contact

  # Function configuration
  function_name = each.value.function_name
  description   = each.value.description
  runtime       = each.value.runtime
  handler       = each.value.handler
  timeout       = each.value.timeout
  memory_size   = each.value.memory_size

  # Source configuration
  source_path = each.value.source_path
  s3_bucket   = each.value.s3_bucket
  s3_key      = each.value.s3_key
  image_uri   = each.value.image_uri

  # Environment variables
  environment_variables = merge(
    each.value.environment_variables,
    {
      ENVIRONMENT = var.environment
      REGION      = var.region
      APPLICATION = var.application
    }
  )

  # VPC configuration (if specified)
  vpc_config = each.value.vpc_enabled ? {
    subnet_ids         = module.networking.private_subnet_ids
    security_group_ids = [module.security_groups.security_group_ids["lambda"]]
  } : null

  # Event source mapping
  event_source_mapping = each.value.event_source_mapping

  # IAM permissions
  additional_iam_policies = each.value.additional_iam_policies
  policy_statements      = each.value.policy_statements

  # Monitoring
  tracing_config_mode               = local.monitoring_config.enable_xray ? "Active" : "PassThrough"
  cloudwatch_logs_retention_in_days = local.monitoring_config.log_retention_days
}

###############################################################
# Outputs
###############################################################

# Networking outputs
output "networking_info" {
  description = "Networking configuration information"
  value = {
    vpc_id              = module.networking.vpc_id
    public_subnet_ids   = module.networking.public_subnet_ids
    private_subnet_ids  = module.networking.private_subnet_ids
    availability_zones  = module.networking.availability_zones
  }
}

# Security outputs
output "security_info" {
  description = "Security configuration information"
  value = {
    security_group_ids = module.security_groups.security_group_ids
  }
}

# Database outputs
output "database_info" {
  description = "Database configuration information"
  value = var.enable_database ? {
    cluster_endpoint = module.database[0].cluster_endpoint
    cluster_arn     = module.database[0].cluster_arn
  } : null
  sensitive = true
}

# Load balancer outputs
output "load_balancer_info" {
  description = "Load balancer configuration information"
  value = var.enable_load_balancer ? {
    dns_name = module.load_balancer[0].dns_name
    arn      = module.load_balancer[0].arn
  } : null
}

# Web servers outputs
output "web_servers_info" {
  description = "Web servers configuration information"
  value = var.enable_web_servers ? {
    autoscaling_group_name = module.web_servers[0].autoscaling_group_name
    launch_template_id     = module.web_servers[0].launch_template_id
  } : null
}

# Lambda functions outputs
output "lambda_functions_info" {
  description = "Lambda functions configuration information"
  value = {
    for name, config in var.lambda_functions_config :
    name => {
      function_name = module.lambda_functions[name].lambda_function_name
      function_arn  = module.lambda_functions[name].lambda_function_arn
      invoke_arn    = module.lambda_functions[name].lambda_function_invoke_arn
    }
  }
}

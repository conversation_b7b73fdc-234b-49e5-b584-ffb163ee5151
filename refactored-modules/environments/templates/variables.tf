# Environment Template Variables
# This file defines variables specific to environment deployments

###############################################################
# Import Shared Variables (Required)
###############################################################

# Core application variables (from shared configuration)
variable "application" {
  description = "Name of the application"
  type        = string
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string
}

variable "environment" {
  description = "Name of the environment"
  type        = string
}

variable "region" {
  description = "AWS Region"
  type        = string
}

variable "region_abbreviated" {
  description = "Abbreviated form of the AWS region"
  type        = string
}

variable "build_number" {
  description = "Build Number for tracking deployments"
  type        = string
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string
}

variable "slack_contact" {
  description = "Slack channel for notifications"
  type        = string
}

# Additional shared variables
variable "vpc_name" {
  description = "Name of the VPC to use for resources"
  type        = string
  default     = "main-vpc"
}

variable "cost_center" {
  description = "Cost center for billing and cost allocation"
  type        = string
  default     = ""
}

variable "data_classification" {
  description = "Data classification level"
  type        = string
  default     = "internal"
}

variable "compliance_framework" {
  description = "Compliance framework to follow"
  type        = string
  default     = ""
}

variable "feature_flags" {
  description = "Feature flags for enabling/disabling functionality"
  type = object({
    enable_waf                = optional(bool, false)
    enable_shield_advanced    = optional(bool, false)
    enable_secrets_manager    = optional(bool, true)
    enable_parameter_store    = optional(bool, true)
    enable_systems_manager    = optional(bool, true)
    enable_inspector         = optional(bool, false)
    enable_macie             = optional(bool, false)
    enable_security_hub      = optional(bool, false)
  })
  default = {}
}

###############################################################
# Component Enable/Disable Flags
###############################################################

variable "enable_database" {
  description = "Whether to deploy the database component"
  type        = bool
  default     = true
}

variable "enable_load_balancer" {
  description = "Whether to deploy the load balancer component"
  type        = bool
  default     = true
}

variable "enable_web_servers" {
  description = "Whether to deploy the web servers component"
  type        = bool
  default     = true
}

###############################################################
# Security Groups Configuration
###############################################################

variable "security_groups_config" {
  description = "Security groups configuration"
  type = map(object({
    description = string
    ingress_rules = list(object({
      description     = string
      from_port       = number
      to_port         = number
      protocol        = string
      cidr_blocks     = optional(list(string), [])
      security_groups = optional(list(string), [])
    }))
    egress_rules = list(object({
      description     = string
      from_port       = number
      to_port         = number
      protocol        = string
      cidr_blocks     = optional(list(string), [])
      security_groups = optional(list(string), [])
    }))
  }))

  default = {
    load-balancer = {
      description = "Security group for application load balancer"
      ingress_rules = [
        {
          description = "HTTPS from internet"
          from_port   = 443
          to_port     = 443
          protocol    = "tcp"
          cidr_blocks = ["0.0.0.0/0"]
        },
        {
          description = "HTTP from internet (redirect to HTTPS)"
          from_port   = 80
          to_port     = 80
          protocol    = "tcp"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }

    web-server = {
      description = "Security group for web servers"
      ingress_rules = [
        {
          description = "HTTP from load balancer"
          from_port   = 80
          to_port     = 80
          protocol    = "tcp"
          cidr_blocks = ["10.0.0.0/8"]
        },
        {
          description = "SSH from trusted networks"
          from_port   = 22
          to_port     = 22
          protocol    = "tcp"
          cidr_blocks = ["************/32", "***********/32"]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }

    database = {
      description = "Security group for database"
      ingress_rules = [
        {
          description = "MySQL from web servers"
          from_port   = 3306
          to_port     = 3306
          protocol    = "tcp"
          cidr_blocks = ["10.0.0.0/8"]
        },
        {
          description = "MySQL from trusted networks"
          from_port   = 3306
          to_port     = 3306
          protocol    = "tcp"
          cidr_blocks = ["************/32", "***********/32"]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }

    lambda = {
      description = "Security group for Lambda functions"
      ingress_rules = []
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }
  }
}

###############################################################
# Database Configuration
###############################################################

variable "database_config" {
  description = "Database configuration"
  type = object({
    component_name                  = optional(string, "webapp-db")
    database_name                   = optional(string, "webapp")
    master_username                 = optional(string, "admin")
    replica_count                   = optional(number, 1)
    kms_key_id                     = optional(string, "")
    enabled_cloudwatch_logs_exports = optional(list(string), ["audit", "error", "general", "slowquery"])
  })

  default = {
    component_name  = "webapp-db"
    database_name   = "webapp"
    master_username = "admin"
    replica_count   = 1
  }
}

###############################################################
# Load Balancer Configuration
###############################################################

variable "load_balancer_config" {
  description = "Load balancer configuration"
  type = object({
    component_name  = optional(string, "frontend")
    internal        = optional(bool, false)
    certificate_arn = string

    target_groups = map(object({
      port     = number
      protocol = string
      health_check = object({
        path                = optional(string, "/health")
        healthy_threshold   = optional(number, 2)
        unhealthy_threshold = optional(number, 3)
        timeout             = optional(number, 5)
        interval            = optional(number, 30)
        matcher             = optional(string, "200")
      })
      stickiness = optional(object({
        enabled = bool
        type    = optional(string, "lb_cookie")
      }), { enabled = false })
    }))

    listeners = map(object({
      port            = number
      protocol        = string
      certificate_arn = optional(string, "")
      default_action = object({
        type             = string
        target_group_arn = optional(string, "")
        redirect = optional(object({
          port        = string
          protocol    = string
          status_code = string
        }), null)
      })
    }))

    waf_config = optional(object({
      enable_rate_limiting = optional(bool, true)
      rate_limit          = optional(number, 2000)
      enable_geo_blocking = optional(bool, false)
      blocked_countries   = optional(list(string), [])
    }), {})
  })

  default = {
    component_name  = "frontend"
    internal        = false
    certificate_arn = ""

    target_groups = {
      web-servers = {
        port     = 80
        protocol = "HTTP"
        health_check = {
          path = "/health"
        }
      }
    }

    listeners = {
      http = {
        port     = 80
        protocol = "HTTP"
        default_action = {
          type = "redirect"
          redirect = {
            port        = "443"
            protocol    = "HTTPS"
            status_code = "HTTP_301"
          }
        }
      }
      https = {
        port     = 443
        protocol = "HTTPS"
        default_action = {
          type             = "forward"
          target_group_arn = "web-servers"
        }
      }
    }
  }
}

###############################################################
# Web Servers Configuration
###############################################################

variable "web_servers_config" {
  description = "Web servers configuration"
  type = object({
    component_name       = optional(string, "frontend")
    key_name            = string
    base_capacity       = optional(number, 1)
    root_volume_size    = optional(number, 30)
    cpu_high_threshold  = optional(number, 75)
    cpu_low_threshold   = optional(number, 25)
    target_group_name   = optional(string, "web-servers")
    user_data_script    = optional(string, "")
  })

  default = {
    component_name    = "frontend"
    key_name         = ""
    base_capacity    = 1
    root_volume_size = 30
    cpu_high_threshold = 75
    cpu_low_threshold  = 25
    target_group_name  = "web-servers"
    user_data_script   = ""
  }
}

###############################################################
# Lambda Functions Configuration
###############################################################

variable "lambda_functions_config" {
  description = "Lambda functions configuration"
  type = map(object({
    service     = string
    component   = string
    function_name = string
    description = string
    runtime     = optional(string, "python3.11")
    handler     = optional(string, "lambda_function.lambda_handler")
    timeout     = optional(number, 30)
    memory_size = optional(number, 128)

    # Source configuration
    source_path = optional(string, "")
    s3_bucket   = optional(string, "")
    s3_key      = optional(string, "")
    image_uri   = optional(string, "")

    # Environment variables
    environment_variables = optional(map(string), {})

    # VPC configuration
    vpc_enabled = optional(bool, false)

    # Event source mapping
    event_source_mapping = optional(map(object({
      event_source_arn                   = string
      batch_size                        = optional(number, 10)
      maximum_batching_window_in_seconds = optional(number, 0)
      starting_position                 = optional(string, "LATEST")
    })), {})

    # IAM permissions
    additional_iam_policies = optional(list(string), [])
    policy_statements = optional(map(object({
      effect    = string
      actions   = list(string)
      resources = list(string)
    })), {})
  }))

  default = {}
}
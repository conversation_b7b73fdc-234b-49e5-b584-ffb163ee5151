# Security Groups Module

This module provides standardized security group creation with best practices for AWS infrastructure. It supports flexible rule configuration while maintaining security standards and consistent naming conventions.

## Features

- **Flexible Configuration**: Define multiple security groups with custom ingress/egress rules
- **Security Best Practices**: Built-in templates for common patterns (web servers, databases, load balancers)
- **Trusted Network Support**: Automatic handling of trusted CIDR blocks
- **Consistent Naming**: Standardized naming conventions for all security groups
- **Rule Validation**: Input validation for security group configurations
- **Comprehensive Tagging**: Automatic tagging with common metadata
- **Lifecycle Management**: Proper lifecycle management to prevent disruptions

## Usage

### Basic Example

```hcl
module "security_groups" {
  source = "../../modules/security/security-groups"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  component               = var.component
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Network configuration
  vpc_id          = module.networking.vpc_id
  homenet_cidr    = var.homenet_cidr
  ais_cidr        = var.ais_cidr
  remote_cidr     = var.remote_cidr
  nfs_cidr        = var.nfs_cidr
  ground_dc_cidrs = var.ground_dc_cidrs

  # Security groups configuration
  security_groups = {
    web-server = {
      description = "Security group for web servers"
      ingress_rules = [
        {
          description = "HTTP from load balancer"
          from_port   = 80
          to_port     = 80
          protocol    = "tcp"
          security_groups = [module.security_groups.security_group_ids["load-balancer"]]
        },
        {
          description = "SSH from trusted networks"
          from_port   = 22
          to_port     = 22
          protocol    = "tcp"
          cidr_blocks = [var.homenet_cidr, var.ais_cidr, var.remote_cidr]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }

    load-balancer = {
      description = "Security group for application load balancer"
      ingress_rules = [
        {
          description = "HTTPS from internet"
          from_port   = 443
          to_port     = 443
          protocol    = "tcp"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }
  }
}
```

### Database Security Group Example

```hcl
module "database_security" {
  source = "../../modules/security/security-groups"

  # ... required variables ...

  security_groups = {
    database = {
      description = "Security group for RDS Aurora cluster"
      ingress_rules = [
        {
          description = "MySQL from application servers"
          from_port   = 3306
          to_port     = 3306
          protocol    = "tcp"
          cidr_blocks = [var.nfs_cidr]
        },
        {
          description = "MySQL from trusted networks for administration"
          from_port   = 3306
          to_port     = 3306
          protocol    = "tcp"
          cidr_blocks = [var.homenet_cidr, var.ais_cidr]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
      tags = {
        Purpose = "Database"
        Tier    = "Data"
      }
    }
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.5.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Resources

- `aws_security_group` - Security groups
- `aws_security_group_rule` - Individual security group rules

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| application | Name of the application | `string` | n/a | yes |
| environment | Name of the environment | `string` | n/a | yes |
| vpc_id | ID of the VPC | `string` | n/a | yes |
| security_groups | Map of security groups to create | `map(object)` | `{}` | no |
| homenet_cidr | HomeNet CIDR block | `string` | n/a | yes |
| ais_cidr | AIS network CIDR block | `string` | n/a | yes |
| remote_cidr | Remote networks CIDR block | `string` | n/a | yes |
| nfs_cidr | NFS network CIDR block | `string` | n/a | yes |
| ground_dc_cidrs | Ground data center CIDR blocks | `list(string)` | `[]` | no |

## Outputs

| Name | Description |
|------|-------------|
| security_group_ids | Map of security group names to IDs |
| security_group_arns | Map of security group names to ARNs |
| security_groups | Complete security group objects |
| security_configuration | Summary of security configuration |

## Security Best Practices

### Implemented Security Features

1. **Least Privilege Access**: Rules are designed with minimal required access
2. **Network Segmentation**: Support for trusted network CIDRs
3. **Rule Descriptions**: All rules require descriptive comments
4. **Lifecycle Management**: Proper resource lifecycle to prevent disruptions
5. **Consistent Naming**: Standardized naming for easy identification

### Security Group Templates

The module includes pre-defined templates for common patterns:

- **Web Server**: HTTP/HTTPS from load balancer, SSH from trusted networks
- **Database**: MySQL/Aurora access from application tier and admin networks
- **Load Balancer**: HTTP/HTTPS from internet with outbound access

### Network Security

- **Trusted Networks**: Automatic handling of HomeNet, AIS, and Remote CIDRs
- **Ground DC Access**: Support for ground data center CIDR blocks
- **NFS Network**: Separate CIDR for internal AWS network access

## Migration from Existing Security Groups

To migrate from existing inline security groups:

1. **Identify Current Rules**: Document existing security group rules
2. **Map to Module Structure**: Convert rules to module configuration format
3. **Test in Non-Production**: Validate configuration in test environment
4. **Gradual Migration**: Migrate one security group at a time

### Migration Example

```hcl
# Old inline security group
resource "aws_security_group" "old_sg" {
  name = "old-security-group"
  # ... rules ...
}

# New module-based security group
module "security_groups" {
  source = "../../modules/security/security-groups"
  # ... configuration ...
}

# Update references
resource "aws_instance" "example" {
  # Old reference
  # vpc_security_group_ids = [aws_security_group.old_sg.id]
  
  # New reference
  vpc_security_group_ids = [module.security_groups.security_group_ids["web-server"]]
}
```

## Troubleshooting

### Common Issues

1. **Circular Dependencies**
   - Cause: Security groups referencing each other
   - Solution: Use separate modules or reference by ID after creation

2. **Rule Conflicts**
   - Cause: Overlapping or conflicting rules
   - Solution: Review rule definitions and ensure no duplicates

3. **Invalid CIDR Blocks**
   - Cause: Malformed CIDR notation
   - Solution: Validate CIDR blocks before applying

### Debugging

- Check security group rules: `aws ec2 describe-security-groups --group-ids <sg-id>`
- Validate CIDR blocks: Use online CIDR calculators
- Test connectivity: Use AWS VPC Reachability Analyzer

## Contributing

When contributing to this module:

1. Follow security best practices
2. Add comprehensive rule descriptions
3. Update documentation for new features
4. Test with multiple scenarios
5. Validate against security standards

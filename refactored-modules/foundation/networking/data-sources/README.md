# Networking Data Sources Module

This module provides standardized data source lookups for existing networking resources including VPCs, subnets, and related infrastructure. It replaces the global data-sources.tf approach with a more modular and reusable pattern.

## Features

- **VPC Discovery**: Lookup existing VPCs by name with optional additional filters
- **Subnet Discovery**: Automatically discover public and private subnets with configurable tagging
- **Multi-VPC Support**: Optional support for Rates & Residuals VPC lookup
- **Availability Zone Mapping**: Automatic discovery of availability zones used by subnets
- **Route Table Discovery**: Lookup route tables associated with subnets
- **Flexible Filtering**: Support for additional custom filters
- **Comprehensive Outputs**: Detailed information about discovered resources

## Usage

### Basic Example

```hcl
module "networking" {
  source = "../../modules/networking/data-sources"

  # Required variables
  application = var.application
  environment = var.environment
  region      = var.region
  vpc_name    = var.vpc_name
}

# Use the outputs in other resources
resource "aws_instance" "example" {
  subnet_id = module.networking.private_subnet_ids[0]
  vpc_security_group_ids = [aws_security_group.example.id]
  # ... other configuration
}
```

### Advanced Example with Custom Filters

```hcl
module "networking" {
  source = "../../modules/networking/data-sources"

  # Required variables
  application = var.application
  environment = var.environment
  region      = var.region
  vpc_name    = var.vpc_name
  
  # Optional Rates & Residuals VPC
  rr_vpc_name = var.rr_vpc_name

  # Custom subnet tagging
  public_subnet_tag_key   = "Type"
  public_subnet_tag_value = "Public"
  private_subnet_tag_key  = "Type"
  private_subnet_tag_value = "Private"

  # Additional filters
  additional_vpc_filters = {
    state_filter = {
      name   = "state"
      values = ["available"]
    }
  }

  additional_subnet_filters = {
    state_filter = {
      name   = "state"
      values = ["available"]
    }
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.5.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Resources

This module only uses data sources and does not create any resources:

- `data.aws_vpc.main` - Primary VPC lookup
- `data.aws_vpc.rates_residuals` - Optional Rates & Residuals VPC lookup
- `data.aws_subnets.public` - Public subnets discovery
- `data.aws_subnets.private` - Private subnets discovery
- `data.aws_subnet.*` - Individual subnet details
- `data.aws_route_tables.*` - Route table discovery

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| application | Name of the application | `string` | n/a | yes |
| environment | Name of the environment | `string` | n/a | yes |
| region | AWS Region | `string` | n/a | yes |
| vpc_name | Name of the VPC to look up | `string` | n/a | yes |
| rr_vpc_name | Name of the Rates & Residuals VPC | `string` | `""` | no |
| public_subnet_tag_key | Tag key to identify public subnets | `string` | `"SUB-Type"` | no |
| public_subnet_tag_value | Tag value to identify public subnets | `string` | `"Public"` | no |
| private_subnet_tag_key | Tag key to identify private subnets | `string` | `"SUB-Type"` | no |
| private_subnet_tag_value | Tag value to identify private subnets | `string` | `"Private"` | no |
| additional_vpc_filters | Additional filters for VPC lookup | `map(object)` | `{}` | no |
| additional_subnet_filters | Additional filters for subnet lookup | `map(object)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | ID of the main VPC |
| vpc_arn | ARN of the main VPC |
| vpc_cidr_block | CIDR block of the main VPC |
| public_subnet_ids | List of public subnet IDs |
| private_subnet_ids | List of private subnet IDs |
| availability_zones | List of all availability zones |
| network_configuration | Summary of network configuration |

## Migration from Global Data Sources

To migrate from the existing `data-sources.global.tf` approach:

1. **Replace data source references**:
   ```hcl
   # Old approach
   vpc_id = data.aws_vpc.vpc.id
   subnet_ids = data.aws_subnet_ids.private.ids

   # New approach
   vpc_id = module.networking.vpc_id
   subnet_ids = module.networking.private_subnet_ids
   ```

2. **Update variable references**:
   ```hcl
   # Old approach
   availability_zones = var.availability_zones

   # New approach
   availability_zones = module.networking.availability_zones
   ```

3. **Remove global data sources**: After migration, remove the global data-sources.tf file

## Security Considerations

- Uses read-only data sources with no resource creation
- Applies consistent tagging for resource identification
- Supports additional filtering for enhanced security
- No sensitive data exposure in outputs

## Troubleshooting

### Common Issues

1. **VPC Not Found**
   - Cause: Incorrect VPC name or missing VPC
   - Solution: Verify VPC name and ensure VPC exists in the target region

2. **No Subnets Found**
   - Cause: Incorrect subnet tags or missing subnets
   - Solution: Verify subnet tagging matches the configured tag key/value pairs

3. **Availability Zone Mismatch**
   - Cause: Subnets not distributed across expected AZs
   - Solution: Check subnet configuration and AZ distribution

### Debugging

- Check VPC existence: `aws ec2 describe-vpcs --filters "Name=tag:Name,Values=<vpc_name>"`
- Check subnet tags: `aws ec2 describe-subnets --filters "Name=vpc-id,Values=<vpc_id>"`
- Verify region: Ensure you're looking in the correct AWS region

# Application Load Balancer Module Outputs

###############################################################
# Load Balancer Information
###############################################################

output "arn" {
  description = "The ARN of the load balancer"
  value       = module.alb.arn
}

output "arn_suffix" {
  description = "The ARN suffix for use with CloudWatch Metrics"
  value       = module.alb.arn_suffix
}

output "dns_name" {
  description = "The DNS name of the load balancer"
  value       = module.alb.dns_name
}

output "zone_id" {
  description = "The canonical hosted zone ID of the load balancer"
  value       = module.alb.zone_id
}

output "id" {
  description = "The ID of the load balancer"
  value       = module.alb.id
}

output "name" {
  description = "The name of the load balancer"
  value       = module.alb.name
}

output "type" {
  description = "The type of load balancer"
  value       = module.alb.type
}

###############################################################
# Target Groups
###############################################################

output "target_groups" {
  description = "Map of target groups created and their attributes"
  value       = module.alb.target_groups
}

output "target_group_arns" {
  description = "ARNs of the target groups"
  value = {
    for key, tg in module.alb.target_groups : key => tg.arn
  }
}

output "target_group_arn_suffixes" {
  description = "ARN suffixes of target groups for use with CloudWatch Metrics"
  value = {
    for key, tg in module.alb.target_groups : key => tg.arn_suffix
  }
}

output "target_group_names" {
  description = "Names of the target groups"
  value = {
    for key, tg in module.alb.target_groups : key => tg.name
  }
}

output "target_group_health_check_paths" {
  description = "Health check paths for target groups"
  value = {
    for key, tg in var.target_groups : key => tg.health_check.path
  }
}

###############################################################
# Listeners
###############################################################

output "listeners" {
  description = "Map of listeners created and their attributes"
  value       = module.alb.listeners
}

output "listener_arns" {
  description = "ARNs of the listeners"
  value = {
    for key, listener in module.alb.listeners : key => listener.arn
  }
}

output "listener_rules" {
  description = "Map of listener rules created and their attributes"
  value       = module.alb.listener_rules
}

###############################################################
# Security Groups
###############################################################

output "security_group_arn" {
  description = "Amazon Resource Name (ARN) of the security group"
  value       = module.alb.security_group_arn
}

output "security_group_id" {
  description = "ID of the security group"
  value       = module.alb.security_group_id
}

###############################################################
# Access Logs
###############################################################

output "access_logs_bucket" {
  description = "S3 bucket used for access logs"
  value       = var.enable_access_logs ? (
    var.access_logs_bucket != "" ? var.access_logs_bucket : aws_s3_bucket.access_logs[0].id
  ) : null
}

output "access_logs_bucket_arn" {
  description = "ARN of the S3 bucket used for access logs"
  value = var.enable_access_logs && var.access_logs_bucket == "" ? aws_s3_bucket.access_logs[0].arn : null
}

###############################################################
# WAF Association
###############################################################

output "waf_acl_arn" {
  description = "ARN of the WAF ACL associated with the load balancer"
  value       = var.enable_waf ? var.waf_acl_arn : null
}

output "waf_association_id" {
  description = "ID of the WAF association"
  value       = var.enable_waf && var.waf_acl_arn != "" ? aws_wafv2_web_acl_association.main[0].id : null
}

###############################################################
# Configuration Summary
###############################################################

output "load_balancer_configuration" {
  description = "Summary of load balancer configuration"
  value = {
    name                    = local.lb_name
    type                    = var.load_balancer_type
    scheme                  = var.internal ? "internal" : "internet-facing"
    vpc_id                  = var.vpc_id
    subnets                 = var.subnet_ids
    security_groups         = var.security_group_ids
    deletion_protection     = var.enable_deletion_protection
    access_logs_enabled     = var.enable_access_logs
    waf_enabled            = var.enable_waf
    cross_zone_lb          = var.enable_cross_zone_load_balancing
    http2_enabled          = var.enable_http2
    ip_address_type        = var.ip_address_type
    idle_timeout           = var.idle_timeout
    target_groups_count    = length(var.target_groups)
    listeners_count        = length(var.listeners)
  }
}

###############################################################
# Connection Information
###############################################################

output "connection_info" {
  description = "Load balancer connection information"
  value = {
    dns_name    = module.alb.dns_name
    zone_id     = module.alb.zone_id
    scheme      = var.internal ? "internal" : "internet-facing"
    ports       = [for key, listener in var.listeners : listener.port]
    protocols   = [for key, listener in var.listeners : listener.protocol]
    ssl_enabled = anytrue([for key, listener in var.listeners : listener.protocol == "HTTPS"])
  }
}

###############################################################
# Monitoring Information
###############################################################

output "monitoring_info" {
  description = "Information for monitoring and alerting"
  value = {
    load_balancer_arn_suffix = module.alb.arn_suffix
    target_group_arn_suffixes = {
      for key, tg in module.alb.target_groups : key => tg.arn_suffix
    }
    cloudwatch_namespace = "AWS/ApplicationELB"
    key_metrics = [
      "RequestCount",
      "TargetResponseTime",
      "HTTPCode_Target_2XX_Count",
      "HTTPCode_Target_4XX_Count",
      "HTTPCode_Target_5XX_Count",
      "UnHealthyHostCount",
      "HealthyHostCount"
    ]
  }
}

###############################################################
# DNS and Routing Information
###############################################################

output "dns_info" {
  description = "DNS and routing information"
  value = {
    dns_name           = module.alb.dns_name
    canonical_zone_id  = module.alb.zone_id
    fqdn              = module.alb.dns_name
    internal          = var.internal
    ip_address_type   = var.ip_address_type
  }
}

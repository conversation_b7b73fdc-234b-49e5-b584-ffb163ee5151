# Application Load Balancer Module Variables

###############################################################
# Required Pass-through Variables
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9]{2,5}$", var.application_abbreviated))
    error_message = "Application abbreviation must be 2-5 characters, lowercase letters and numbers only."
  }
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string
  default     = "web"

  validation {
    condition = contains([
      "web", "api", "database", "processing", "monitoring", "security"
    ], var.service)
    error_message = "Service must be one of: web, api, database, processing, monitoring, security."
  }
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string

  validation {
    condition     = length(var.component) > 0 && can(regex("^[a-z0-9-]+$", var.component))
    error_message = "Component name must be non-empty and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Name of the environment"
  type        = string

  validation {
    condition = contains([
      "production", "nonprod", "homenet", "qamain",
      "qarapid", "scratch", "uat"
    ], var.environment)
    error_message = "Environment must be one of: production, nonprod, homenet, qamain, qarapid, scratch, uat."
  }
}

variable "region" {
  description = "AWS Region"
  type        = string

  validation {
    condition = contains([
      "us-east-1", "us-west-2"
    ], var.region)
    error_message = "Region must be one of the supported regions: us-east-1, us-west-2."
  }
}

variable "region_abbreviated" {
  description = "Abbreviated form of the AWS region"
  type        = string

  validation {
    condition = contains([
      "ue1", "uw2"
    ], var.region_abbreviated)
    error_message = "Region abbreviation must be one of: ue1, uw2."
  }
}

variable "build_number" {
  description = "Build Number for tracking deployments"
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.build_number))
    error_message = "Build number must be in semantic version format (e.g., 1.0.0)."
  }
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string

  validation {
    condition     = length(var.launched_by) > 0
    error_message = "Launched by cannot be empty."
  }
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string

  validation {
    condition     = can(formatdate("RFC3339", var.launched_on))
    error_message = "Launched on must be a valid RFC3339 timestamp."
  }
}

variable "slack_contact" {
  description = "Slack channel for notifications"
  type        = string

  validation {
    condition     = can(regex("^[+#@][a-zA-Z0-9-_]+$", var.slack_contact))
    error_message = "Slack contact must start with +, #, or @ followed by valid channel/user name."
  }
}

###############################################################
# Network Configuration
###############################################################

variable "vpc_id" {
  description = "ID of the VPC where the load balancer will be created"
  type        = string

  validation {
    condition     = can(regex("^vpc-[a-z0-9]+$", var.vpc_id))
    error_message = "VPC ID must be a valid AWS VPC identifier."
  }
}

variable "subnet_ids" {
  description = "List of subnet IDs for the load balancer"
  type        = list(string)

  validation {
    condition     = length(var.subnet_ids) >= 2
    error_message = "At least 2 subnet IDs must be provided for high availability."
  }
}

variable "security_group_ids" {
  description = "List of security group IDs to associate with the load balancer"
  type        = list(string)
  default     = []
}

###############################################################
# Load Balancer Configuration
###############################################################

variable "load_balancer_type" {
  description = "Type of load balancer to create"
  type        = string
  default     = "application"

  validation {
    condition = contains([
      "application", "network", "gateway"
    ], var.load_balancer_type)
    error_message = "Load balancer type must be application, network, or gateway."
  }
}

variable "internal" {
  description = "Whether the load balancer is internal"
  type        = bool
  default     = false
}

variable "enable_deletion_protection" {
  description = "Enable deletion protection for the load balancer"
  type        = bool
  default     = true
}

variable "idle_timeout" {
  description = "The time in seconds that the connection is allowed to be idle"
  type        = number
  default     = 60

  validation {
    condition     = var.idle_timeout >= 1 && var.idle_timeout <= 4000
    error_message = "Idle timeout must be between 1 and 4000 seconds."
  }
}

variable "enable_cross_zone_load_balancing" {
  description = "Enable cross-zone load balancing"
  type        = bool
  default     = true
}

variable "enable_http2" {
  description = "Enable HTTP/2 for the load balancer"
  type        = bool
  default     = true
}

variable "ip_address_type" {
  description = "The type of IP addresses used by the subnets"
  type        = string
  default     = "ipv4"

  validation {
    condition = contains([
      "ipv4", "dualstack"
    ], var.ip_address_type)
    error_message = "IP address type must be ipv4 or dualstack."
  }
}

###############################################################
# Access Logging Configuration
###############################################################

variable "enable_access_logs" {
  description = "Enable access logs for the load balancer"
  type        = bool
  default     = true
}

variable "access_logs_bucket" {
  description = "S3 bucket for access logs"
  type        = string
  default     = ""
}

variable "access_logs_prefix" {
  description = "S3 prefix for access logs"
  type        = string
  default     = ""
}

###############################################################
# SSL/TLS Configuration
###############################################################

variable "certificate_arn" {
  description = "ARN of the SSL certificate for HTTPS listeners"
  type        = string
  default     = ""
}

variable "ssl_policy" {
  description = "SSL policy for HTTPS listeners"
  type        = string
  default     = "ELBSecurityPolicy-TLS-1-2-2017-01"

  validation {
    condition = contains([
      "ELBSecurityPolicy-TLS-1-2-2017-01",
      "ELBSecurityPolicy-TLS-1-2-Ext-2018-06",
      "ELBSecurityPolicy-FS-2018-06",
      "ELBSecurityPolicy-FS-1-2-2019-08",
      "ELBSecurityPolicy-FS-1-2-Res-2019-08",
      "ELBSecurityPolicy-FS-1-2-Res-2020-10",
      "ELBSecurityPolicy-TLS-1-1-2017-01",
      "ELBSecurityPolicy-2016-08"
    ], var.ssl_policy)
    error_message = "SSL policy must be a valid ELB security policy."
  }
}

###############################################################
# Target Groups Configuration
###############################################################

variable "target_groups" {
  description = "Map of target groups to create"
  type = map(object({
    port                 = number
    protocol             = string
    target_type          = optional(string, "instance")
    vpc_id              = optional(string, "")
    health_check = optional(object({
      enabled             = optional(bool, true)
      healthy_threshold   = optional(number, 2)
      unhealthy_threshold = optional(number, 2)
      timeout             = optional(number, 5)
      interval            = optional(number, 30)
      path                = optional(string, "/")
      matcher             = optional(string, "200")
      port                = optional(string, "traffic-port")
      protocol            = optional(string, "HTTP")
    }), {})
    stickiness = optional(object({
      enabled         = optional(bool, false)
      type           = optional(string, "lb_cookie")
      cookie_duration = optional(number, 86400)
    }), {})
    tags = optional(map(string), {})
  }))
  default = {}
}

###############################################################
# WAF Configuration
###############################################################

variable "enable_waf" {
  description = "Enable WAF association with the load balancer"
  type        = bool
  default     = false
}

variable "waf_acl_arn" {
  description = "ARN of the WAF ACL to associate with the load balancer"
  type        = string
  default     = ""
}

###############################################################
# Common Resource Tags
###############################################################

locals {
  common_tags = {
    Application  = var.application
    Service      = var.service
    Component    = var.component
    Environment  = var.environment
    Region       = var.region
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    ManagedBy    = "terraform"
    Module       = "load-balancer-alb"
  }

  # Generate load balancer name
  lb_name = "${var.application}-${var.component}-${var.environment}"

  # Generate target group names
  tg_names = {
    for key, tg in var.target_groups : key => "${local.lb_name}-${key}"
  }
}

###############################################################
# Listeners Configuration
###############################################################

variable "listeners" {
  description = "Map of listeners to create"
  type = map(object({
    port     = number
    protocol = string
    ssl_policy = optional(string, "")
    certificate_arn = optional(string, "")
    default_action = object({
      type             = string
      target_group_arn = optional(string, "")
      redirect = optional(object({
        port        = string
        protocol    = string
        status_code = string
      }), null)
      fixed_response = optional(object({
        content_type = string
        message_body = string
        status_code  = string
      }), null)
    })
    rules = optional(list(object({
      priority = number
      conditions = list(object({
        field  = string
        values = list(string)
      }))
      actions = list(object({
        type             = string
        target_group_arn = optional(string, "")
        redirect = optional(object({
          port        = string
          protocol    = string
          status_code = string
        }), null)
        fixed_response = optional(object({
          content_type = string
          message_body = string
          status_code  = string
        }), null)
      }))
    })), [])
  }))
  default = {}
}

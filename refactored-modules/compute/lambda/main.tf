# Lambda Function Module
# This module creates a Lambda function using the official Terraform AWS modules

###############################################################
# Data Sources
###############################################################

# Get current AWS region
data "aws_region" "current" {}

# Get current caller identity
data "aws_caller_identity" "current" {}

###############################################################
# Source Code Archive (if using local source)
###############################################################

data "archive_file" "lambda_zip" {
  count       = var.source_path != "" && var.filename == "" ? 1 : 0
  type        = "zip"
  source_dir  = var.source_path
  output_path = "${path.module}/lambda_function.zip"
}

###############################################################
# Lambda Function using Official Module
###############################################################

module "lambda_function" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "~> 7.0"

  function_name = local.function_name
  description   = local.function_description
  handler       = var.handler
  runtime       = var.runtime
  architectures = var.architectures
  publish       = var.publish
  timeout       = var.timeout
  memory_size   = var.memory_size
  package_type  = var.package_type

  # Source configuration
  filename         = var.filename != "" ? var.filename : (var.source_path != "" ? data.archive_file.lambda_zip[0].output_path : null)
  s3_bucket        = var.s3_bucket != "" ? var.s3_bucket : null
  s3_key           = var.s3_key != "" ? var.s3_key : null
  s3_object_version = var.s3_object_version != "" ? var.s3_object_version : null
  image_uri        = var.image_uri != "" ? var.image_uri : null

  # Image configuration (for container images)
  image_config_command           = var.image_config.command
  image_config_entry_point       = var.image_config.entry_point
  image_config_working_directory = var.image_config.working_directory

  # Environment variables
  environment_variables = var.environment_variables
  kms_key_arn          = var.kms_key_arn != "" ? var.kms_key_arn : null

  # VPC configuration
  vpc_subnet_ids         = var.vpc_config != null ? var.vpc_config.subnet_ids : null
  vpc_security_group_ids = var.vpc_config != null ? var.vpc_config.security_group_ids : null

  # Dead letter queue
  dead_letter_target_arn = var.dead_letter_config != null ? var.dead_letter_config.target_arn : null

  # Tracing
  tracing_config_mode = var.tracing_config_mode

  # Layers
  layers = var.layers

  # Concurrency
  reserved_concurrent_executions = var.reserved_concurrent_executions

  # IAM configuration
  create_role                    = var.create_role
  lambda_role                   = var.lambda_role != "" ? var.lambda_role : null
  attach_cloudwatch_logs_policy = var.attach_cloudwatch_logs_policy
  attach_dead_letter_policy     = local.attach_dead_letter_policy
  attach_network_policy         = local.attach_network_policy
  attach_tracing_policy         = local.attach_tracing_policy
  attach_async_event_policy     = var.attach_async_event_policy

  # Additional IAM policies
  attach_policies    = length(var.additional_iam_policies) > 0
  policies           = var.additional_iam_policies
  number_of_policies = length(var.additional_iam_policies)

  # Policy statements
  attach_policy_statements = length(var.policy_statements) > 0
  policy_statements        = var.policy_statements

  # CloudWatch Logs
  cloudwatch_logs_retention_in_days = var.cloudwatch_logs_retention_in_days
  cloudwatch_logs_kms_key_id       = var.cloudwatch_logs_kms_key_id != "" ? var.cloudwatch_logs_kms_key_id : null
  create_cloudwatch_log_group      = var.create_cloudwatch_log_group

  # Async configuration
  destination_on_failure                = var.destination_config != null && var.destination_config.on_failure != null ? var.destination_config.on_failure.destination : null
  destination_on_success                = var.destination_config != null && var.destination_config.on_success != null ? var.destination_config.on_success.destination : null
  maximum_event_age_in_seconds         = var.maximum_event_age_in_seconds
  maximum_retry_attempts               = var.maximum_retry_attempts

  # Tags
  tags = merge(
    local.common_tags,
    {
      Name = "lambda-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "LambdaFunction"
    }
  )

  depends_on = [data.archive_file.lambda_zip]
}

###############################################################
# Event Source Mapping
###############################################################

resource "aws_lambda_event_source_mapping" "this" {
  for_each = var.event_source_mapping

  event_source_arn  = each.value.event_source_arn
  function_name     = each.value.function_name != "" ? each.value.function_name : module.lambda_function.lambda_function_name
  enabled           = each.value.enabled
  batch_size        = each.value.batch_size
  maximum_batching_window_in_seconds = each.value.maximum_batching_window_in_seconds
  parallelization_factor            = each.value.parallelization_factor
  starting_position                 = each.value.starting_position
  starting_position_timestamp       = each.value.starting_position_timestamp != "" ? each.value.starting_position_timestamp : null
  maximum_record_age_in_seconds     = each.value.maximum_record_age_in_seconds != -1 ? each.value.maximum_record_age_in_seconds : null
  bisect_batch_on_function_error    = each.value.bisect_batch_on_function_error
  maximum_retry_attempts            = each.value.maximum_retry_attempts != -1 ? each.value.maximum_retry_attempts : null
  tumbling_window_in_seconds        = each.value.tumbling_window_in_seconds != 0 ? each.value.tumbling_window_in_seconds : null
  topics                            = length(each.value.topics) > 0 ? each.value.topics : null
  queues                            = length(each.value.queues) > 0 ? each.value.queues : null
  function_response_types           = length(each.value.function_response_types) > 0 ? each.value.function_response_types : null

  # Source access configuration
  dynamic "source_access_configuration" {
    for_each = each.value.source_access_configuration
    content {
      type = source_access_configuration.value.type
      uri  = source_access_configuration.value.uri
    }
  }

  # Self-managed event source
  dynamic "self_managed_event_source" {
    for_each = each.value.self_managed_event_source != null ? [each.value.self_managed_event_source] : []
    content {
      endpoints = self_managed_event_source.value.endpoints
    }
  }

  # Amazon managed Kafka event source config
  dynamic "amazon_managed_kafka_event_source_config" {
    for_each = each.value.amazon_managed_kafka_event_source_config != null ? [each.value.amazon_managed_kafka_event_source_config] : []
    content {
      consumer_group_id = amazon_managed_kafka_event_source_config.value.consumer_group_id
    }
  }

  # Self-managed Kafka event source config
  dynamic "self_managed_kafka_event_source_config" {
    for_each = each.value.self_managed_kafka_event_source_config != null ? [each.value.self_managed_kafka_event_source_config] : []
    content {
      consumer_group_id = self_managed_kafka_event_source_config.value.consumer_group_id
    }
  }

  depends_on = [module.lambda_function]
}

###############################################################
# Provisioned Concurrency Configuration
###############################################################

resource "aws_lambda_provisioned_concurrency_config" "this" {
  count                             = var.provisioned_concurrency_config != null ? 1 : 0
  function_name                     = module.lambda_function.lambda_function_name
  provisioned_concurrent_executions = var.provisioned_concurrency_config.provisioned_concurrent_executions
  qualifier                         = var.provisioned_concurrency_config.qualifier

  depends_on = [module.lambda_function]
}

###############################################################
# CloudWatch Alarms
###############################################################

# Error rate alarm
resource "aws_cloudwatch_metric_alarm" "error_rate" {
  alarm_name          = "${local.function_name}-error-rate"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Sum"
  threshold           = "5"
  alarm_description   = "This metric monitors lambda error rate"
  alarm_actions       = []

  dimensions = {
    FunctionName = module.lambda_function.lambda_function_name
  }

  tags = merge(
    local.common_tags,
    {
      Name = "alarm-errors-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "CloudWatchAlarm"
    }
  )

  depends_on = [module.lambda_function]
}

# Duration alarm
resource "aws_cloudwatch_metric_alarm" "duration" {
  alarm_name          = "${local.function_name}-duration"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Duration"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Average"
  threshold           = var.timeout * 1000 * 0.8  # 80% of timeout in milliseconds
  alarm_description   = "This metric monitors lambda duration"
  alarm_actions       = []

  dimensions = {
    FunctionName = module.lambda_function.lambda_function_name
  }

  tags = merge(
    local.common_tags,
    {
      Name = "alarm-duration-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "CloudWatchAlarm"
    }
  )

  depends_on = [module.lambda_function]
}

# Throttles alarm
resource "aws_cloudwatch_metric_alarm" "throttles" {
  alarm_name          = "${local.function_name}-throttles"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Throttles"
  namespace           = "AWS/Lambda"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "This metric monitors lambda throttles"
  alarm_actions       = []

  dimensions = {
    FunctionName = module.lambda_function.lambda_function_name
  }

  tags = merge(
    local.common_tags,
    {
      Name = "alarm-throttles-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "CloudWatchAlarm"
    }
  )

  depends_on = [module.lambda_function]
}

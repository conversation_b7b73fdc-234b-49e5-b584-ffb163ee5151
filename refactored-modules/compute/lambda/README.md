# Lambda Function Module

This module creates AWS Lambda functions using the official Terraform AWS modules with comprehensive configuration options, monitoring, and best practices. It supports multiple deployment methods and advanced features like VPC integration, event source mapping, and provisioned concurrency.

## Features

- **Official Module Integration**: Uses the official `terraform-aws-modules/lambda/aws` module
- **Multiple Deployment Methods**: ZIP files, S3 objects, or container images
- **Event Source Mapping**: Support for SQS, Kinesis, DynamoDB, and other event sources
- **VPC Integration**: Deploy functions in private subnets with security groups
- **Dead Letter Queues**: Error handling with DLQ configuration
- **Provisioned Concurrency**: Reduce cold start latency
- **X-Ray Tracing**: Distributed tracing for performance monitoring
- **CloudWatch Integration**: Comprehensive logging and monitoring
- **IAM Best Practices**: Least privilege access with custom policies

## Usage

### Basic Python Function

```hcl
module "simple_lambda" {
  source = "../../refactored-modules/compute/lambda"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "api"
  component               = "user-service"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Function configuration
  function_name = "user-api"
  description   = "User management API"
  runtime       = "python3.11"
  handler       = "app.lambda_handler"
  timeout       = 30
  memory_size   = 512

  # Source code from local directory
  source_path = "${path.module}/lambda-code/user-api"

  # Environment variables
  environment_variables = {
    ENVIRONMENT   = var.environment
    LOG_LEVEL    = "INFO"
    DATABASE_URL = var.database_url
  }
}
```

### Event-Driven Processing with SQS

```hcl
module "event_processor" {
  source = "../../refactored-modules/compute/lambda"

  # ... required variables ...

  # Function configuration
  function_name = "event-processor"
  runtime       = "python3.11"
  handler       = "processor.lambda_handler"
  timeout       = 300
  memory_size   = 1024

  # Source code from S3
  s3_bucket = "my-lambda-deployments"
  s3_key    = "event-processor/v1.0.0/function.zip"

  # Event source mapping
  event_source_mapping = {
    sqs_events = {
      event_source_arn                   = aws_sqs_queue.events.arn
      batch_size                        = 10
      maximum_batching_window_in_seconds = 5
      maximum_retry_attempts            = 3
    }
  }

  # Dead letter queue
  dead_letter_config = {
    target_arn = aws_sqs_queue.dlq.arn
  }

  # IAM permissions
  policy_statements = {
    sqs_access = {
      effect = "Allow"
      actions = [
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:GetQueueAttributes"
      ]
      resources = [aws_sqs_queue.events.arn]
    }
  }
}
```

### Container-Based Lambda

```hcl
module "container_lambda" {
  source = "../../refactored-modules/compute/lambda"

  # ... required variables ...

  # Container configuration
  package_type = "Image"
  image_uri    = "123456789012.dkr.ecr.us-east-1.amazonaws.com/my-lambda:latest"
  
  image_config = {
    command           = ["app.handler"]
    entry_point       = ["/lambda-entrypoint.sh"]
    working_directory = "/var/task"
  }

  # Function configuration
  timeout     = 900
  memory_size = 3008
  
  # VPC configuration for private resources
  vpc_config = {
    subnet_ids         = var.private_subnet_ids
    security_group_ids = [aws_security_group.lambda.id]
  }
}
```

### Scheduled Function with Provisioned Concurrency

```hcl
module "scheduled_lambda" {
  source = "../../refactored-modules/compute/lambda"

  # ... required variables ...

  # Function configuration
  function_name = "daily-report"
  runtime       = "nodejs18.x"
  handler       = "index.handler"
  timeout       = 600
  memory_size   = 1024

  # Source code
  source_path = "${path.module}/lambda-code/daily-report"

  # Provisioned concurrency for consistent performance
  provisioned_concurrency_config = {
    provisioned_concurrent_executions = 5
    qualifier                         = "$LATEST"
  }

  # X-Ray tracing
  tracing_config_mode = "Active"

  # Custom CloudWatch log retention
  cloudwatch_logs_retention_in_days = 30
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.5.0 |
| aws | ~> 5.0 |
| archive | ~> 2.2 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |
| archive | ~> 2.2 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| lambda_function | terraform-aws-modules/lambda/aws | ~> 7.0 |

## Resources

- `aws_lambda_event_source_mapping` - Event source mappings
- `aws_lambda_provisioned_concurrency_config` - Provisioned concurrency
- `aws_cloudwatch_metric_alarm` - CloudWatch alarms
- `data.archive_file` - Source code archive (if using local source)

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| application | Name of the application | `string` | n/a | yes |
| environment | Name of the environment | `string` | n/a | yes |
| runtime | Lambda runtime | `string` | `"python3.11"` | no |
| handler | Function handler | `string` | `"lambda_function.lambda_handler"` | no |
| timeout | Function timeout in seconds | `number` | `30` | no |
| memory_size | Memory allocation in MB | `number` | `128` | no |
| source_path | Path to source code directory | `string` | `""` | no |
| environment_variables | Environment variables | `map(string)` | `{}` | no |
| vpc_config | VPC configuration | `object` | `null` | no |

## Outputs

| Name | Description |
|------|-------------|
| lambda_function_arn | Lambda function ARN |
| lambda_function_name | Lambda function name |
| lambda_function_invoke_arn | Lambda function invoke ARN |
| lambda_role_arn | IAM role ARN |
| monitoring_info | Monitoring configuration |

## Event Source Types

### Supported Event Sources
- **Amazon SQS**: Queue-based event processing
- **Amazon Kinesis**: Stream processing
- **Amazon DynamoDB**: Database change streams
- **Amazon MSK**: Kafka message processing
- **Amazon MQ**: Message queue integration
- **Self-managed Kafka**: Custom Kafka clusters

### Event Source Configuration
```hcl
event_source_mapping = {
  kinesis_stream = {
    event_source_arn                   = aws_kinesis_stream.example.arn
    starting_position                  = "LATEST"
    batch_size                        = 100
    maximum_batching_window_in_seconds = 5
    parallelization_factor            = 10
  }
}
```

## Security Features

### IAM Integration
- Automatic IAM role creation with least privilege
- Support for additional IAM policies
- Custom policy statements for specific permissions
- VPC execution role when VPC is configured

### Network Security
- VPC deployment in private subnets
- Security group integration
- Network isolation from internet

### Data Protection
- Environment variable encryption with KMS
- CloudWatch logs encryption
- X-Ray tracing for security monitoring

## Monitoring and Observability

### CloudWatch Integration
- Automatic log group creation
- Configurable log retention
- Custom log group encryption

### Built-in Alarms
- **Error Rate**: Monitors function errors
- **Duration**: Tracks execution time
- **Throttles**: Detects concurrency limits

### X-Ray Tracing
- Distributed tracing support
- Performance analysis
- Service map visualization

## Performance Optimization

### Memory and CPU
- Right-size memory allocation
- Monitor duration metrics
- Use provisioned concurrency for consistent performance

### Cold Start Reduction
- Provisioned concurrency configuration
- Container image optimization
- Runtime selection considerations

### Cost Optimization
- ARM64 architecture support for better price-performance
- Appropriate timeout settings
- Memory optimization based on usage patterns

## Best Practices Implemented

1. **Security**: Least privilege IAM, encryption, VPC isolation
2. **Monitoring**: Comprehensive CloudWatch integration
3. **Error Handling**: Dead letter queues, retry configuration
4. **Performance**: Provisioned concurrency, optimized settings
5. **Cost**: Right-sized resources, efficient architectures
6. **Maintainability**: Consistent naming, comprehensive tagging

## Troubleshooting

### Common Issues

1. **Function Timeout**
   - Increase timeout value
   - Optimize function code
   - Check external dependencies

2. **Memory Errors**
   - Increase memory allocation
   - Monitor memory usage metrics
   - Optimize memory usage in code

3. **VPC Connectivity**
   - Check security group rules
   - Verify subnet configuration
   - Ensure NAT gateway for internet access

### Debugging Tools
- CloudWatch Logs for function output
- X-Ray traces for performance analysis
- CloudWatch metrics for monitoring
- AWS CLI for function testing

## Migration from Existing Functions

### From Manual Lambda Configuration
```hcl
# Old manual configuration
resource "aws_lambda_function" "old_function" {
  filename         = "function.zip"
  function_name    = "old-function"
  # ... configuration ...
}

# New module-based configuration
module "new_function" {
  source = "../../refactored-modules/compute/lambda"
  # ... configuration ...
}
```

### Update References
```hcl
# Old reference
resource "aws_api_gateway_integration" "lambda" {
  uri = aws_lambda_function.old_function.invoke_arn
}

# New reference
resource "aws_api_gateway_integration" "lambda" {
  uri = module.new_function.lambda_function_invoke_arn
}
```

## Contributing

When contributing to this module:

1. Test with multiple runtimes and configurations
2. Validate event source mapping functionality
3. Ensure security best practices
4. Test VPC and non-VPC deployments
5. Update documentation for new features

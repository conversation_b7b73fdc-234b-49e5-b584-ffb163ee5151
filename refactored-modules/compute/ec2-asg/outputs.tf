# EC2 Auto Scaling Group Module Outputs

###############################################################
# Auto Scaling Group Information
###############################################################

output "autoscaling_group_id" {
  description = "The Auto Scaling Group ID"
  value       = module.asg.autoscaling_group_id
}

output "autoscaling_group_name" {
  description = "The Auto Scaling Group name"
  value       = module.asg.autoscaling_group_name
}

output "autoscaling_group_arn" {
  description = "The ARN for this Auto Scaling Group"
  value       = module.asg.autoscaling_group_arn
}

output "autoscaling_group_min_size" {
  description = "The minimum size of the Auto Scaling Group"
  value       = module.asg.autoscaling_group_min_size
}

output "autoscaling_group_max_size" {
  description = "The maximum size of the Auto Scaling Group"
  value       = module.asg.autoscaling_group_max_size
}

output "autoscaling_group_desired_capacity" {
  description = "The number of Amazon EC2 instances that should be running in the group"
  value       = module.asg.autoscaling_group_desired_capacity
}

output "autoscaling_group_default_cooldown" {
  description = "Time between a scaling activity and the succeeding scaling activity"
  value       = module.asg.autoscaling_group_default_cooldown
}

output "autoscaling_group_health_check_grace_period" {
  description = "Time after instance launch before checking health"
  value       = module.asg.autoscaling_group_health_check_grace_period
}

output "autoscaling_group_health_check_type" {
  description = "EC2 or ELB. Controls how health checking is done"
  value       = module.asg.autoscaling_group_health_check_type
}

output "autoscaling_group_availability_zones" {
  description = "The availability zones of the Auto Scaling Group"
  value       = module.asg.autoscaling_group_availability_zones
}

output "autoscaling_group_vpc_zone_identifier" {
  description = "The VPC zone identifier"
  value       = module.asg.autoscaling_group_vpc_zone_identifier
}

output "autoscaling_group_load_balancers" {
  description = "The load balancer names associated with the Auto Scaling Group"
  value       = module.asg.autoscaling_group_load_balancers
}

output "autoscaling_group_target_group_arns" {
  description = "List of Target Group ARNs that apply to this Auto Scaling Group"
  value       = module.asg.autoscaling_group_target_group_arns
}

###############################################################
# Launch Template Information
###############################################################

output "launch_template_id" {
  description = "The ID of the launch template"
  value       = aws_launch_template.this.id
}

output "launch_template_arn" {
  description = "The ARN of the launch template"
  value       = aws_launch_template.this.arn
}

output "launch_template_name" {
  description = "The name of the launch template"
  value       = aws_launch_template.this.name
}

output "launch_template_latest_version" {
  description = "The latest version of the launch template"
  value       = aws_launch_template.this.latest_version
}

output "launch_template_default_version" {
  description = "The default version of the launch template"
  value       = aws_launch_template.this.default_version
}

###############################################################
# IAM Information
###############################################################

output "iam_role_name" {
  description = "The name of the IAM role"
  value       = var.create_iam_instance_profile ? aws_iam_role.instance_role[0].name : null
}

output "iam_role_arn" {
  description = "The Amazon Resource Name (ARN) specifying the IAM role"
  value       = var.create_iam_instance_profile ? aws_iam_role.instance_role[0].arn : null
}

output "iam_role_unique_id" {
  description = "Stable and unique string identifying the IAM role"
  value       = var.create_iam_instance_profile ? aws_iam_role.instance_role[0].unique_id : null
}

output "iam_instance_profile_arn" {
  description = "ARN assigned by AWS to the instance profile"
  value       = var.create_iam_instance_profile ? aws_iam_instance_profile.this[0].arn : null
}

output "iam_instance_profile_name" {
  description = "Name of the instance profile"
  value       = var.create_iam_instance_profile ? aws_iam_instance_profile.this[0].name : var.iam_instance_profile_name
}

output "iam_instance_profile_unique_id" {
  description = "Unique ID assigned by AWS to the instance profile"
  value       = var.create_iam_instance_profile ? aws_iam_instance_profile.this[0].unique_id : null
}

###############################################################
# Scaling Policies
###############################################################

output "scale_up_policy_arn" {
  description = "The ARN assigned by AWS to the scaling up policy"
  value       = var.enable_scaling_policies ? aws_autoscaling_policy.scale_up[0].arn : null
}

output "scale_up_policy_name" {
  description = "The scaling up policy's name"
  value       = var.enable_scaling_policies ? aws_autoscaling_policy.scale_up[0].name : null
}

output "scale_down_policy_arn" {
  description = "The ARN assigned by AWS to the scaling down policy"
  value       = var.enable_scaling_policies ? aws_autoscaling_policy.scale_down[0].arn : null
}

output "scale_down_policy_name" {
  description = "The scaling down policy's name"
  value       = var.enable_scaling_policies ? aws_autoscaling_policy.scale_down[0].name : null
}

###############################################################
# CloudWatch Alarms
###############################################################

output "cpu_high_alarm_id" {
  description = "The ID of the high CPU alarm"
  value       = var.enable_scaling_policies ? aws_cloudwatch_metric_alarm.cpu_high[0].id : null
}

output "cpu_high_alarm_arn" {
  description = "The ARN of the high CPU alarm"
  value       = var.enable_scaling_policies ? aws_cloudwatch_metric_alarm.cpu_high[0].arn : null
}

output "cpu_low_alarm_id" {
  description = "The ID of the low CPU alarm"
  value       = var.enable_scaling_policies ? aws_cloudwatch_metric_alarm.cpu_low[0].id : null
}

output "cpu_low_alarm_arn" {
  description = "The ARN of the low CPU alarm"
  value       = var.enable_scaling_policies ? aws_cloudwatch_metric_alarm.cpu_low[0].arn : null
}

###############################################################
# CloudWatch Log Groups
###############################################################

output "cloudwatch_log_groups" {
  description = "Map of CloudWatch log groups created"
  value = {
    system    = aws_cloudwatch_log_group.system.name
    security  = aws_cloudwatch_log_group.security.name
    user_data = aws_cloudwatch_log_group.user_data.name
  }
}

output "cloudwatch_log_group_arns" {
  description = "Map of CloudWatch log group ARNs"
  value = {
    system    = aws_cloudwatch_log_group.system.arn
    security  = aws_cloudwatch_log_group.security.arn
    user_data = aws_cloudwatch_log_group.user_data.arn
  }
}

###############################################################
# Instance Configuration
###############################################################

output "instance_configuration" {
  description = "Summary of instance configuration"
  value = {
    instance_type               = var.instance_type
    ami_id                     = var.ami_id != "" ? var.ami_id : data.aws_ami.this[0].id
    key_name                   = var.key_name
    associate_public_ip_address = var.associate_public_ip_address
    enable_monitoring          = var.enable_monitoring
    ebs_optimized             = var.ebs_optimized
    root_volume_size          = var.root_volume_size
    root_volume_type          = var.root_volume_type
    root_volume_encrypted     = var.root_volume_encrypted
    security_group_ids        = var.security_group_ids
    subnet_ids               = var.subnet_ids
  }
}

###############################################################
# Auto Scaling Configuration
###############################################################

output "autoscaling_configuration" {
  description = "Summary of Auto Scaling configuration"
  value = {
    min_size                  = var.min_size
    max_size                  = var.max_size
    desired_capacity          = var.desired_capacity
    health_check_type         = var.health_check_type
    health_check_grace_period = var.health_check_grace_period
    default_cooldown          = var.default_cooldown
    termination_policies      = var.termination_policies
    suspended_processes       = var.suspended_processes
    protect_from_scale_in     = var.protect_from_scale_in
    enabled_metrics          = var.enabled_metrics
    scaling_policies_enabled  = var.enable_scaling_policies
    cpu_high_threshold       = var.cpu_utilization_high_threshold
    cpu_low_threshold        = var.cpu_utilization_low_threshold
  }
}

###############################################################
# Monitoring Information
###############################################################

output "monitoring_info" {
  description = "Information for monitoring and alerting"
  value = {
    autoscaling_group_name = module.asg.autoscaling_group_name
    cloudwatch_namespace   = "AWS/EC2"
    cloudwatch_log_groups  = {
      system    = aws_cloudwatch_log_group.system.name
      security  = aws_cloudwatch_log_group.security.name
      user_data = aws_cloudwatch_log_group.user_data.name
    }
    key_metrics = [
      "CPUUtilization",
      "DiskReadOps",
      "DiskWriteOps",
      "NetworkIn",
      "NetworkOut",
      "StatusCheckFailed",
      "StatusCheckFailed_Instance",
      "StatusCheckFailed_System"
    ]
    asg_metrics = var.enabled_metrics
  }
}

###############################################################
# Security Information
###############################################################

output "security_info" {
  description = "Security configuration information"
  value = {
    security_group_ids        = var.security_group_ids
    iam_instance_profile_name = var.create_iam_instance_profile ? aws_iam_instance_profile.this[0].name : var.iam_instance_profile_name
    root_volume_encrypted     = var.root_volume_encrypted
    metadata_options = {
      http_endpoint = "enabled"
      http_tokens   = "required"
    }
    ssm_agent_enabled        = var.enable_ssm_agent
    cloudwatch_agent_enabled = var.enable_cloudwatch_agent
  }
}

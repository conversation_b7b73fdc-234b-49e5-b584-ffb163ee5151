# EC2 Auto Scaling Group Module

This module creates an EC2 Auto Scaling Group with Launch Template using the official Terraform AWS modules. It provides a standardized way to deploy scalable compute resources with enhanced security, monitoring, and automation.

## Features

- **Official Module Integration**: Uses the official `terraform-aws-modules/autoscaling/aws` module
- **Launch Template**: Modern launch template with comprehensive configuration
- **Auto Scaling**: Configurable scaling policies with CloudWatch alarms
- **Security**: IAM roles, encrypted storage, security groups, IMDSv2
- **Monitoring**: CloudWatch agent, SSM agent, comprehensive logging
- **Instance Refresh**: Rolling updates with configurable strategies
- **Load Balancer Integration**: Support for ALB target groups and classic ELBs
- **User Data**: Automated instance configuration and monitoring setup

## Usage

### Basic Web Server Example

```hcl
module "web_servers" {
  source = "../../modules/compute/ec2-asg"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "web"
  component               = "frontend"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["web-server"]]

  # Instance configuration
  instance_type = "t3.medium"
  key_name      = var.ec2_key_name
  
  # Auto Scaling configuration
  min_size         = 2
  max_size         = 6
  desired_capacity = 3

  # Load balancer integration
  target_group_arns = [module.alb.target_group_arns["web-servers"]]
  health_check_type = "ELB"

  # Storage configuration
  root_volume_size      = 30
  root_volume_type      = "gp3"
  root_volume_encrypted = true

  # Monitoring
  enable_cloudwatch_agent = true
  enable_ssm_agent        = true
  enable_monitoring       = true

  # Scaling policies
  enable_scaling_policies         = true
  cpu_utilization_high_threshold  = 75
  cpu_utilization_low_threshold   = 25
}
```

### API Server with Custom User Data

```hcl
module "api_servers" {
  source = "../../modules/compute/ec2-asg"

  # ... required variables ...

  # Instance configuration
  instance_type = "m5.large"
  ami_id        = var.custom_api_ami_id

  # Custom user data script
  user_data_script = templatefile("${path.module}/api-user-data.sh", {
    api_version     = var.api_version
    database_url    = module.aurora.cluster_endpoint
    redis_endpoint  = module.elasticache.primary_endpoint
  })

  # Auto Scaling configuration
  min_size         = 3
  max_size         = 12
  desired_capacity = 6

  # Advanced scaling configuration
  scale_up_adjustment   = 2
  scale_down_adjustment = -1
  scale_up_cooldown     = 180
  scale_down_cooldown   = 300

  # Additional EBS volumes
  additional_ebs_volumes = [
    {
      device_name = "/dev/sdf"
      volume_size = 100
      volume_type = "gp3"
      encrypted   = true
    }
  ]

  # Additional IAM policies
  additional_iam_policies = [
    "arn:aws:iam::aws:policy/AmazonS3ReadOnlyAccess",
    aws_iam_policy.api_custom_policy.arn
  ]
}
```

### Processing Workers Example

```hcl
module "processing_workers" {
  source = "../../modules/compute/ec2-asg"

  # ... required variables ...

  # Compute-optimized instances
  instance_type = "c5.2xlarge"
  
  # Auto Scaling configuration for batch processing
  min_size         = 0
  max_size         = 20
  desired_capacity = 2

  # Spot instances for cost optimization
  mixed_instances_policy = {
    instances_distribution = {
      on_demand_base_capacity                  = 1
      on_demand_percentage_above_base_capacity = 25
      spot_allocation_strategy                 = "diversified"
    }
    launch_template = {
      override = [
        {
          instance_type     = "c5.2xlarge"
          weighted_capacity = "1"
        },
        {
          instance_type     = "c5.4xlarge"
          weighted_capacity = "2"
        }
      ]
    }
  }

  # Custom termination policies for batch processing
  termination_policies = ["OldestInstance"]

  # Disable scaling policies (use external triggers)
  enable_scaling_policies = false

  # Custom metrics for processing workloads
  enabled_metrics = [
    "GroupMinSize",
    "GroupMaxSize",
    "GroupDesiredCapacity",
    "GroupInServiceInstances",
    "GroupTotalInstances",
    "GroupPendingInstances",
    "GroupStandbyInstances",
    "GroupTerminatingInstances"
  ]
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.5.0 |
| aws | ~> 5.0 |
| cloudinit | ~> 2.2 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |
| cloudinit | ~> 2.2 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| asg | terraform-aws-modules/autoscaling/aws | ~> 7.0 |

## Resources

- `aws_launch_template` - Launch template for instances
- `aws_iam_role` - IAM role for instances
- `aws_iam_instance_profile` - Instance profile
- `aws_autoscaling_policy` - Scaling policies
- `aws_cloudwatch_metric_alarm` - CloudWatch alarms
- `aws_cloudwatch_log_group` - Log groups

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| application | Name of the application | `string` | n/a | yes |
| environment | Name of the environment | `string` | n/a | yes |
| vpc_id | VPC ID | `string` | n/a | yes |
| subnet_ids | List of subnet IDs | `list(string)` | n/a | yes |
| instance_type | EC2 instance type | `string` | `"t3.medium"` | no |
| min_size | Minimum ASG size | `number` | `1` | no |
| max_size | Maximum ASG size | `number` | `3` | no |
| desired_capacity | Desired ASG capacity | `number` | `2` | no |
| enable_scaling_policies | Enable auto scaling | `bool` | `true` | no |

## Outputs

| Name | Description |
|------|-------------|
| autoscaling_group_name | Auto Scaling Group name |
| autoscaling_group_arn | Auto Scaling Group ARN |
| launch_template_id | Launch template ID |
| iam_instance_profile_name | Instance profile name |
| monitoring_info | Monitoring configuration |

## Security Features

### Instance Security
- **IMDSv2 Enforced**: Instance metadata service v2 required
- **Encrypted Storage**: EBS volumes encrypted by default
- **Security Groups**: Network-level access control
- **IAM Roles**: Least privilege access with instance profiles

### Monitoring and Logging
- **CloudWatch Agent**: System and application metrics
- **SSM Agent**: Systems Manager integration
- **CloudWatch Logs**: Centralized log collection
- **Enhanced Monitoring**: Detailed instance metrics

### Access Control
- **Key Pair Authentication**: SSH key-based access
- **IAM Policies**: Granular permission control
- **Security Groups**: Network access restrictions
- **Systems Manager**: Secure shell access without SSH

## Best Practices Implemented

1. **High Availability**: Multi-AZ deployment
2. **Auto Scaling**: Responsive to demand changes
3. **Security**: Encryption, IAM roles, security groups
4. **Monitoring**: Comprehensive metrics and logging
5. **Automation**: User data scripts for configuration
6. **Cost Optimization**: Right-sized instances and scaling
7. **Maintenance**: Instance refresh for updates

## User Data Script

The module includes a comprehensive user data script that:

- Updates system packages
- Installs CloudWatch and SSM agents
- Configures monitoring and logging
- Sets up security tools (fail2ban)
- Enables automatic security updates
- Creates health check scripts
- Tags instances appropriately

### Custom User Data

You can provide custom user data in three ways:

1. **Base64 encoded**: Use `user_data_base64` variable
2. **Plain text**: Use `user_data_script` variable
3. **Template file**: Use `templatefile()` function

## Monitoring and Alerting

### Key Metrics
- **CPU Utilization**: Instance CPU usage
- **Memory Usage**: RAM utilization
- **Disk Usage**: Storage utilization
- **Network I/O**: Network traffic
- **Auto Scaling Metrics**: Group size and health

### CloudWatch Alarms
- High CPU utilization (scale up)
- Low CPU utilization (scale down)
- Instance health checks
- Custom application metrics

### Log Groups
- **System Logs**: `/aws/ec2/{app}/{env}/system`
- **Security Logs**: `/aws/ec2/{app}/{env}/security`
- **User Data Logs**: `/aws/ec2/{app}/{env}/user-data`

## Troubleshooting

### Common Issues

1. **Instance Launch Failures**
   - Check AMI availability and permissions
   - Verify subnet and security group configuration
   - Review IAM instance profile permissions

2. **Auto Scaling Issues**
   - Check CloudWatch alarm configuration
   - Verify scaling policy settings
   - Review health check configuration

3. **User Data Script Failures**
   - Check `/var/log/user-data.log` on instances
   - Verify IAM permissions for CloudWatch and SSM
   - Review script syntax and dependencies

### Debugging Commands

```bash
# Check Auto Scaling Group status
aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names <name>

# View instance health
aws autoscaling describe-auto-scaling-instances

# Check launch template
aws ec2 describe-launch-templates --launch-template-names <name>

# View user data logs
aws logs get-log-events --log-group-name "/aws/ec2/{app}/{env}/user-data"
```

## Contributing

When contributing to this module:

1. Test with multiple instance types and configurations
2. Validate auto scaling behavior
3. Ensure security best practices
4. Test user data scripts thoroughly
5. Update documentation for new features

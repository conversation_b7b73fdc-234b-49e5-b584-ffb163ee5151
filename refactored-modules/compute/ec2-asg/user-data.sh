#!/bin/bash
# Default User Data Script for EC2 Instances
# This script sets up basic monitoring and management tools

set -e

# Variables from Terraform
APPLICATION="${application}"
ENVIRONMENT="${environment}"
COMPONENT="${component}"
ENABLE_CLOUDWATCH_AGENT="${enable_cloudwatch_agent}"
ENABLE_SSM_AGENT="${enable_ssm_agent}"

# Log all output
exec > >(tee /var/log/user-data.log)
exec 2>&1

echo "Starting user data script execution at $(date)"
echo "Application: $APPLICATION"
echo "Environment: $ENVIRONMENT"
echo "Component: $COMPONENT"

# Update system packages
echo "Updating system packages..."
yum update -y

# Install basic utilities
echo "Installing basic utilities..."
yum install -y \
    wget \
    curl \
    unzip \
    htop \
    jq \
    awscli \
    amazon-cloudwatch-agent \
    amazon-ssm-agent

# Configure CloudWatch Agent if enabled
if [ "$ENABLE_CLOUDWATCH_AGENT" = "true" ]; then
    echo "Configuring CloudWatch Agent..."
    
    # Create CloudWatch Agent configuration
    cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << EOF
{
    "agent": {
        "metrics_collection_interval": 60,
        "run_as_user": "cwagent"
    },
    "metrics": {
        "namespace": "AIS/$APPLICATION/$ENVIRONMENT",
        "metrics_collected": {
            "cpu": {
                "measurement": [
                    "cpu_usage_idle",
                    "cpu_usage_iowait",
                    "cpu_usage_user",
                    "cpu_usage_system"
                ],
                "metrics_collection_interval": 60,
                "totalcpu": false
            },
            "disk": {
                "measurement": [
                    "used_percent"
                ],
                "metrics_collection_interval": 60,
                "resources": [
                    "*"
                ]
            },
            "diskio": {
                "measurement": [
                    "io_time"
                ],
                "metrics_collection_interval": 60,
                "resources": [
                    "*"
                ]
            },
            "mem": {
                "measurement": [
                    "mem_used_percent"
                ],
                "metrics_collection_interval": 60
            },
            "netstat": {
                "measurement": [
                    "tcp_established",
                    "tcp_time_wait"
                ],
                "metrics_collection_interval": 60
            },
            "swap": {
                "measurement": [
                    "swap_used_percent"
                ],
                "metrics_collection_interval": 60
            }
        }
    },
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [
                    {
                        "file_path": "/var/log/messages",
                        "log_group_name": "/aws/ec2/$APPLICATION/$ENVIRONMENT/system",
                        "log_stream_name": "{instance_id}/messages"
                    },
                    {
                        "file_path": "/var/log/secure",
                        "log_group_name": "/aws/ec2/$APPLICATION/$ENVIRONMENT/security",
                        "log_stream_name": "{instance_id}/secure"
                    },
                    {
                        "file_path": "/var/log/user-data.log",
                        "log_group_name": "/aws/ec2/$APPLICATION/$ENVIRONMENT/user-data",
                        "log_stream_name": "{instance_id}/user-data"
                    }
                ]
            }
        }
    }
}
EOF

    # Start CloudWatch Agent
    /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
        -a fetch-config \
        -m ec2 \
        -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json \
        -s
    
    # Enable CloudWatch Agent service
    systemctl enable amazon-cloudwatch-agent
    systemctl start amazon-cloudwatch-agent
    
    echo "CloudWatch Agent configured and started"
fi

# Configure SSM Agent if enabled
if [ "$ENABLE_SSM_AGENT" = "true" ]; then
    echo "Configuring SSM Agent..."
    
    # Enable and start SSM Agent
    systemctl enable amazon-ssm-agent
    systemctl start amazon-ssm-agent
    
    echo "SSM Agent configured and started"
fi

# Set up instance tags for identification
INSTANCE_ID=$(curl -s http://169.254.169.254/latest/meta-data/instance-id)
REGION=$(curl -s http://169.254.169.254/latest/meta-data/placement/region)

echo "Instance ID: $INSTANCE_ID"
echo "Region: $REGION"

# Add custom tags to the instance
aws ec2 create-tags \
    --region "$REGION" \
    --resources "$INSTANCE_ID" \
    --tags \
        Key=Name,Value="$APPLICATION-$COMPONENT-$ENVIRONMENT-$INSTANCE_ID" \
        Key=UserDataCompleted,Value="$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
        Key=UserDataVersion,Value="1.0"

# Create a status file to indicate user data completion
echo "$(date -u +%Y-%m-%dT%H:%M:%SZ)" > /var/log/user-data-completed

# Set up log rotation for user data log
cat > /etc/logrotate.d/user-data << EOF
/var/log/user-data.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF

# Install and configure fail2ban for basic security
echo "Installing and configuring fail2ban..."
yum install -y fail2ban

cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/secure
EOF

systemctl enable fail2ban
systemctl start fail2ban

# Configure automatic security updates
echo "Configuring automatic security updates..."
yum install -y yum-cron

sed -i 's/update_cmd = default/update_cmd = security/' /etc/yum/yum-cron.conf
sed -i 's/apply_updates = no/apply_updates = yes/' /etc/yum/yum-cron.conf

systemctl enable yum-cron
systemctl start yum-cron

# Set up basic monitoring script
cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/bash
# Basic health check script

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 90 ]; then
    echo "WARNING: Disk usage is at $DISK_USAGE%"
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEM_USAGE" -gt 90 ]; then
    echo "WARNING: Memory usage is at $MEM_USAGE%"
fi

# Check load average
LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
CPU_COUNT=$(nproc)
LOAD_THRESHOLD=$(echo "$CPU_COUNT * 2" | bc)

if (( $(echo "$LOAD_AVG > $LOAD_THRESHOLD" | bc -l) )); then
    echo "WARNING: Load average ($LOAD_AVG) is high for $CPU_COUNT CPUs"
fi

echo "Health check completed at $(date)"
EOF

chmod +x /usr/local/bin/health-check.sh

# Set up cron job for health checks
echo "*/5 * * * * root /usr/local/bin/health-check.sh >> /var/log/health-check.log 2>&1" >> /etc/crontab

echo "User data script completed successfully at $(date)"
echo "Instance is ready for service"

# EC2 Auto Scaling Group Module
# This module creates an Auto Scaling Group with Launch Template using official Terraform AWS modules

###############################################################
# Data Sources
###############################################################

# Get latest AMI if not specified
data "aws_ami" "this" {
  count       = var.ami_id == "" ? 1 : 0
  most_recent = true
  owners      = [var.ami_owner]

  filter {
    name   = "name"
    values = [var.ami_name_filter]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "state"
    values = ["available"]
  }
}

# Get current region
data "aws_region" "current" {}

# Get current caller identity
data "aws_caller_identity" "current" {}

###############################################################
# IAM Role and Instance Profile
###############################################################

# IAM role for EC2 instances
resource "aws_iam_role" "instance_role" {
  count = var.create_iam_instance_profile ? 1 : 0
  name  = "${local.name_prefix}-instance-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    local.common_tags,
    {
      Name = "role-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "IAMRole"
    }
  )
}

# Attach basic policies to the instance role
resource "aws_iam_role_policy_attachment" "ssm_managed_instance_core" {
  count      = var.create_iam_instance_profile && var.enable_ssm_agent ? 1 : 0
  role       = aws_iam_role.instance_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "cloudwatch_agent_server_policy" {
  count      = var.create_iam_instance_profile && var.enable_cloudwatch_agent ? 1 : 0
  role       = aws_iam_role.instance_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

# Attach additional policies
resource "aws_iam_role_policy_attachment" "additional_policies" {
  count      = var.create_iam_instance_profile ? length(var.additional_iam_policies) : 0
  role       = aws_iam_role.instance_role[0].name
  policy_arn = var.additional_iam_policies[count.index]
}

# Custom policy for EC2 tagging
resource "aws_iam_role_policy" "ec2_tagging" {
  count = var.create_iam_instance_profile ? 1 : 0
  name  = "${local.name_prefix}-ec2-tagging"
  role  = aws_iam_role.instance_role[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateTags",
          "ec2:DescribeTags",
          "ec2:DescribeInstances"
        ]
        Resource = "*"
      }
    ]
  })
}

# Instance profile
resource "aws_iam_instance_profile" "this" {
  count = var.create_iam_instance_profile ? 1 : 0
  name  = "${local.name_prefix}-instance-profile"
  role  = aws_iam_role.instance_role[0].name

  tags = merge(
    local.common_tags,
    {
      Name = "ip-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "InstanceProfile"
    }
  )
}

###############################################################
# Launch Template
###############################################################

resource "aws_launch_template" "this" {
  name_prefix   = "${local.name_prefix}-"
  image_id      = var.ami_id != "" ? var.ami_id : data.aws_ami.this[0].id
  instance_type = var.instance_type
  key_name      = var.key_name != "" ? var.key_name : null

  vpc_security_group_ids = var.security_group_ids

  iam_instance_profile {
    name = var.create_iam_instance_profile ? aws_iam_instance_profile.this[0].name : var.iam_instance_profile_name
  }

  # Root volume configuration
  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      volume_size           = var.root_volume_size
      volume_type           = var.root_volume_type
      encrypted             = var.root_volume_encrypted
      kms_key_id           = var.root_volume_kms_key_id != "" ? var.root_volume_kms_key_id : null
      delete_on_termination = true
    }
  }

  # Additional EBS volumes
  dynamic "block_device_mappings" {
    for_each = var.additional_ebs_volumes
    content {
      device_name = block_device_mappings.value.device_name
      ebs {
        volume_size           = block_device_mappings.value.volume_size
        volume_type           = block_device_mappings.value.volume_type
        encrypted             = block_device_mappings.value.encrypted
        kms_key_id           = block_device_mappings.value.kms_key_id != "" ? block_device_mappings.value.kms_key_id : null
        iops                 = block_device_mappings.value.iops
        throughput           = block_device_mappings.value.throughput
        delete_on_termination = true
      }
    }
  }

  # Network configuration
  network_interfaces {
    associate_public_ip_address = var.associate_public_ip_address
    security_groups            = var.security_group_ids
    delete_on_termination      = true
  }

  # Monitoring
  monitoring {
    enabled = var.enable_monitoring
  }

  # EBS optimization
  ebs_optimized = var.ebs_optimized

  # User data
  user_data = local.user_data

  # Instance metadata options
  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                = "required"
    http_put_response_hop_limit = 1
    instance_metadata_tags      = "enabled"
  }

  tag_specifications {
    resource_type = "instance"
    tags = merge(
      local.common_tags,
      {
        Name = "${local.name_prefix}-instance"
        Type = "EC2Instance"
      }
    )
  }

  tag_specifications {
    resource_type = "volume"
    tags = merge(
      local.common_tags,
      {
        Name = "${local.name_prefix}-volume"
        Type = "EBSVolume"
      }
    )
  }

  tags = merge(
    local.common_tags,
    {
      Name = "lt-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "LaunchTemplate"
    }
  )

  lifecycle {
    create_before_destroy = true
  }
}

###############################################################
# Auto Scaling Group using Official Module
###############################################################

module "asg" {
  source  = "terraform-aws-modules/autoscaling/aws"
  version = "~> 7.0"

  name = local.name_prefix

  # Launch template configuration
  launch_template_name        = aws_launch_template.this.name
  launch_template_version     = "$Latest"
  launch_template_description = "Launch template for ${local.name_prefix}"

  # Auto Scaling Group configuration
  min_size                  = var.min_size
  max_size                  = var.max_size
  desired_capacity          = var.desired_capacity
  vpc_zone_identifier       = var.subnet_ids
  health_check_type         = var.health_check_type
  health_check_grace_period = var.health_check_grace_period
  default_cooldown          = var.default_cooldown
  termination_policies      = var.termination_policies
  suspended_processes       = var.suspended_processes
  protect_from_scale_in     = var.protect_from_scale_in

  # Load balancer integration
  target_group_arns = var.target_group_arns
  load_balancers    = var.load_balancers

  # Metrics
  enabled_metrics = var.enabled_metrics

  # Instance refresh
  instance_refresh = {
    strategy = "Rolling"
    preferences = {
      checkpoint_delay       = 600
      checkpoint_percentages = [35, 70, 100]
      instance_warmup        = 300
      min_healthy_percentage = 50
    }
    triggers = ["tag"]
  }

  # Tags
  tags = merge(
    local.common_tags,
    {
      Name = "asg-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "AutoScalingGroup"
    }
  )

  depends_on = [aws_launch_template.this]
}

###############################################################
# Auto Scaling Policies
###############################################################

# Scale up policy
resource "aws_autoscaling_policy" "scale_up" {
  count                  = var.enable_scaling_policies ? 1 : 0
  name                   = "${local.name_prefix}-scale-up"
  scaling_adjustment     = var.scale_up_adjustment
  adjustment_type        = "ChangeInCapacity"
  cooldown              = var.scale_up_cooldown
  autoscaling_group_name = module.asg.autoscaling_group_name
  policy_type           = "SimpleScaling"

  depends_on = [module.asg]
}

# Scale down policy
resource "aws_autoscaling_policy" "scale_down" {
  count                  = var.enable_scaling_policies ? 1 : 0
  name                   = "${local.name_prefix}-scale-down"
  scaling_adjustment     = var.scale_down_adjustment
  adjustment_type        = "ChangeInCapacity"
  cooldown              = var.scale_down_cooldown
  autoscaling_group_name = module.asg.autoscaling_group_name
  policy_type           = "SimpleScaling"

  depends_on = [module.asg]
}

###############################################################
# CloudWatch Alarms
###############################################################

# High CPU utilization alarm
resource "aws_cloudwatch_metric_alarm" "cpu_high" {
  count               = var.enable_scaling_policies ? 1 : 0
  alarm_name          = "${local.name_prefix}-cpu-high"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Average"
  threshold           = var.cpu_utilization_high_threshold
  alarm_description   = "This metric monitors ec2 cpu utilization"
  alarm_actions       = [aws_autoscaling_policy.scale_up[0].arn]

  dimensions = {
    AutoScalingGroupName = module.asg.autoscaling_group_name
  }

  tags = merge(
    local.common_tags,
    {
      Name = "alarm-cpu-high-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "CloudWatchAlarm"
    }
  )

  depends_on = [module.asg, aws_autoscaling_policy.scale_up]
}

# Low CPU utilization alarm
resource "aws_cloudwatch_metric_alarm" "cpu_low" {
  count               = var.enable_scaling_policies ? 1 : 0
  alarm_name          = "${local.name_prefix}-cpu-low"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Average"
  threshold           = var.cpu_utilization_low_threshold
  alarm_description   = "This metric monitors ec2 cpu utilization"
  alarm_actions       = [aws_autoscaling_policy.scale_down[0].arn]

  dimensions = {
    AutoScalingGroupName = module.asg.autoscaling_group_name
  }

  tags = merge(
    local.common_tags,
    {
      Name = "alarm-cpu-low-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "CloudWatchAlarm"
    }
  )

  depends_on = [module.asg, aws_autoscaling_policy.scale_down]
}

###############################################################
# CloudWatch Log Groups
###############################################################

# System logs
resource "aws_cloudwatch_log_group" "system" {
  name              = "/aws/ec2/${var.application}/${var.environment}/system"
  retention_in_days = 30

  tags = merge(
    local.common_tags,
    {
      Name = "lg-system-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "LogGroup"
      LogType = "System"
    }
  )
}

# Security logs
resource "aws_cloudwatch_log_group" "security" {
  name              = "/aws/ec2/${var.application}/${var.environment}/security"
  retention_in_days = 90

  tags = merge(
    local.common_tags,
    {
      Name = "lg-security-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "LogGroup"
      LogType = "Security"
    }
  )
}

# User data logs
resource "aws_cloudwatch_log_group" "user_data" {
  name              = "/aws/ec2/${var.application}/${var.environment}/user-data"
  retention_in_days = 14

  tags = merge(
    local.common_tags,
    {
      Name = "lg-userdata-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "LogGroup"
      LogType = "UserData"
    }
  )
}

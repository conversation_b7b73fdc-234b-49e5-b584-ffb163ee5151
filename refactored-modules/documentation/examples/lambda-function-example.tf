# Lambda Function Example
# This example demonstrates how to use the Lambda module for various use cases

###############################################################
# Common Variables
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string
  default     = "webapp"
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string
  default     = "wa"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "region" {
  description = "AWS Region"
  type        = string
  default     = "us-east-1"
}

variable "region_abbreviated" {
  description = "Abbreviated region"
  type        = string
  default     = "ue1"
}

variable "build_number" {
  description = "Build number"
  type        = string
  default     = "1.0.0"
}

variable "launched_by" {
  description = "Who launched this infrastructure"
  type        = string
  default     = "terraform"
}

variable "launched_on" {
  description = "When this was launched"
  type        = string
  default     = "2024-01-01T00:00:00Z"
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
  default     = "+ops-team"
}

###############################################################
# Example 1: Simple API Lambda Function
###############################################################

module "api_lambda" {
  source = "../compute/lambda"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "api"
  component               = "user-service"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Function configuration
  function_name = "user-api"
  description   = "User management API"
  runtime       = "python3.11"
  handler       = "app.lambda_handler"
  timeout       = 30
  memory_size   = 512

  # Source code
  source_path = "${path.module}/lambda-code/user-api"

  # Environment variables
  environment_variables = {
    ENVIRONMENT    = var.environment
    LOG_LEVEL     = "INFO"
    DATABASE_URL  = "mysql://webapp.cluster-xxx.us-east-1.rds.amazonaws.com:3306/webapp"
    CORS_ORIGINS  = "https://webapp.example.com"
  }

  # IAM permissions
  additional_iam_policies = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
  ]

  policy_statements = {
    rds_access = {
      effect = "Allow"
      actions = [
        "rds-db:connect"
      ]
      resources = [
        "arn:aws:rds-db:${var.region}:*:dbuser:*/lambda-user"
      ]
    }
  }

  # Monitoring
  tracing_config_mode = "Active"
}

###############################################################
# Example 2: Event-Driven Processing Lambda
###############################################################

module "event_processor" {
  source = "../compute/lambda"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "processing"
  component               = "event-processor"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Function configuration
  function_name = "event-processor"
  description   = "Process events from SQS queue"
  runtime       = "python3.11"
  handler       = "processor.lambda_handler"
  timeout       = 300  # 5 minutes for processing
  memory_size   = 1024

  # Source code
  source_path = "${path.module}/lambda-code/event-processor"

  # Environment variables
  environment_variables = {
    ENVIRONMENT     = var.environment
    LOG_LEVEL      = "INFO"
    OUTPUT_BUCKET  = "webapp-processed-data-${var.environment}"
    BATCH_SIZE     = "10"
  }

  # Event source mapping for SQS
  event_source_mapping = {
    sqs_events = {
      event_source_arn = aws_sqs_queue.events.arn
      batch_size       = 10
      maximum_batching_window_in_seconds = 5
    }
  }

  # Dead letter queue configuration
  dead_letter_config = {
    target_arn = aws_sqs_queue.dlq.arn
  }

  # Async configuration
  maximum_retry_attempts = 2
  maximum_event_age_in_seconds = 3600

  # IAM permissions
  policy_statements = {
    sqs_access = {
      effect = "Allow"
      actions = [
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:GetQueueAttributes"
      ]
      resources = [
        aws_sqs_queue.events.arn
      ]
    }
    s3_access = {
      effect = "Allow"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl"
      ]
      resources = [
        "arn:aws:s3:::webapp-processed-data-${var.environment}/*"
      ]
    }
  }

  # Monitoring
  tracing_config_mode = "Active"
}

###############################################################
# Example 3: Scheduled Lambda Function
###############################################################

module "scheduled_task" {
  source = "../compute/lambda"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "processing"
  component               = "cleanup-task"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Function configuration
  function_name = "cleanup-task"
  description   = "Daily cleanup of old data"
  runtime       = "python3.11"
  handler       = "cleanup.lambda_handler"
  timeout       = 900  # 15 minutes
  memory_size   = 256

  # Source code
  source_path = "${path.module}/lambda-code/cleanup-task"

  # Environment variables
  environment_variables = {
    ENVIRONMENT      = var.environment
    LOG_LEVEL       = "INFO"
    RETENTION_DAYS  = "30"
    DATABASE_URL    = "mysql://webapp.cluster-xxx.us-east-1.rds.amazonaws.com:3306/webapp"
  }

  # IAM permissions
  policy_statements = {
    rds_access = {
      effect = "Allow"
      actions = [
        "rds-db:connect"
      ]
      resources = [
        "arn:aws:rds-db:${var.region}:*:dbuser:*/lambda-user"
      ]
    }
    logs_access = {
      effect = "Allow"
      actions = [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "logs:DeleteLogGroup"
      ]
      resources = [
        "arn:aws:logs:${var.region}:*:log-group:/aws/lambda/cleanup-*"
      ]
    }
  }

  # Monitoring
  tracing_config_mode = "Active"
}

###############################################################
# Supporting Resources
###############################################################

# SQS Queue for event processing
resource "aws_sqs_queue" "events" {
  name                      = "${var.application}-events-${var.environment}"
  delay_seconds             = 0
  max_message_size          = 262144
  message_retention_seconds = 1209600  # 14 days
  receive_wait_time_seconds = 20
  
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.dlq.arn
    maxReceiveCount     = 3
  })

  tags = {
    Application = var.application
    Environment = var.environment
    Component   = "event-processing"
  }
}

# Dead Letter Queue
resource "aws_sqs_queue" "dlq" {
  name                      = "${var.application}-events-dlq-${var.environment}"
  message_retention_seconds = 1209600  # 14 days

  tags = {
    Application = var.application
    Environment = var.environment
    Component   = "event-processing"
  }
}

# EventBridge rule for scheduled task
resource "aws_cloudwatch_event_rule" "daily_cleanup" {
  name                = "${var.application}-daily-cleanup-${var.environment}"
  description         = "Trigger daily cleanup task"
  schedule_expression = "cron(0 2 * * ? *)"  # Daily at 2 AM UTC

  tags = {
    Application = var.application
    Environment = var.environment
    Component   = "cleanup"
  }
}

# EventBridge target
resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.daily_cleanup.name
  target_id = "CleanupLambdaTarget"
  arn       = module.scheduled_task.lambda_function_arn
}

# Permission for EventBridge to invoke Lambda
resource "aws_lambda_permission" "allow_eventbridge" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = module.scheduled_task.lambda_function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.daily_cleanup.arn
}

###############################################################
# Outputs
###############################################################

output "api_lambda_function_name" {
  description = "Name of the API Lambda function"
  value       = module.api_lambda.lambda_function_name
}

output "api_lambda_invoke_arn" {
  description = "Invoke ARN of the API Lambda function"
  value       = module.api_lambda.lambda_function_invoke_arn
}

output "event_processor_function_name" {
  description = "Name of the event processor Lambda function"
  value       = module.event_processor.lambda_function_name
}

output "scheduled_task_function_name" {
  description = "Name of the scheduled task Lambda function"
  value       = module.scheduled_task.lambda_function_name
}

output "sqs_queue_url" {
  description = "URL of the SQS queue"
  value       = aws_sqs_queue.events.url
}

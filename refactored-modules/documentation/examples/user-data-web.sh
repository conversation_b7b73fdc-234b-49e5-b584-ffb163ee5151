#!/bin/bash
# Web Application User Data Script
# This script configures a web server instance

set -e

# Variables from Terraform
DATABASE_ENDPOINT="${database_endpoint}"
APPLICATION_NAME="${application_name}"
ENVIRONMENT="${environment}"

# Log all output
exec > >(tee /var/log/user-data-web.log)
exec 2>&1

echo "Starting web application setup at $(date)"
echo "Application: $APPLICATION_NAME"
echo "Environment: $ENVIRONMENT"
echo "Database Endpoint: $DATABASE_ENDPOINT"

# Update system packages
echo "Updating system packages..."
yum update -y

# Install web server and dependencies
echo "Installing web server and dependencies..."
yum install -y \
    httpd \
    php \
    php-mysql \
    php-json \
    php-curl \
    mysql \
    wget \
    curl \
    unzip

# Configure Apache
echo "Configuring Apache web server..."
systemctl enable httpd
systemctl start httpd

# Create a simple health check endpoint
cat > /var/www/html/health << 'EOF'
OK
EOF

# Create a simple info page
cat > /var/www/html/info.php << EOF
<?php
echo "<h1>$APPLICATION_NAME - $ENVIRONMENT</h1>";
echo "<p>Server: " . gethostname() . "</p>";
echo "<p>Time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Database: $DATABASE_ENDPOINT</p>";
phpinfo();
?>
EOF

# Create application configuration
mkdir -p /opt/webapp/config
cat > /opt/webapp/config/database.php << EOF
<?php
return [
    'host' => '$DATABASE_ENDPOINT',
    'port' => 3306,
    'database' => 'webapp',
    'charset' => 'utf8mb4',
];
?>
EOF

# Set proper permissions
chown -R apache:apache /var/www/html
chown -R apache:apache /opt/webapp
chmod -R 755 /var/www/html
chmod -R 755 /opt/webapp

# Configure firewall
echo "Configuring firewall..."
systemctl enable firewalld
systemctl start firewalld
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --reload

# Install and configure CloudWatch agent (if not already done by base user data)
if ! command -v /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl &> /dev/null; then
    echo "Installing CloudWatch agent..."
    wget https://s3.amazonaws.com/amazoncloudwatch-agent/amazon_linux/amd64/latest/amazon-cloudwatch-agent.rpm
    rpm -U ./amazon-cloudwatch-agent.rpm
fi

# Configure CloudWatch agent for web application
cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent-web.json << EOF
{
    "agent": {
        "metrics_collection_interval": 60,
        "run_as_user": "cwagent"
    },
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [
                    {
                        "file_path": "/var/log/httpd/access_log",
                        "log_group_name": "/aws/ec2/$APPLICATION_NAME/$ENVIRONMENT/apache-access",
                        "log_stream_name": "{instance_id}/access"
                    },
                    {
                        "file_path": "/var/log/httpd/error_log",
                        "log_group_name": "/aws/ec2/$APPLICATION_NAME/$ENVIRONMENT/apache-error",
                        "log_stream_name": "{instance_id}/error"
                    },
                    {
                        "file_path": "/var/log/user-data-web.log",
                        "log_group_name": "/aws/ec2/$APPLICATION_NAME/$ENVIRONMENT/user-data-web",
                        "log_stream_name": "{instance_id}/user-data-web"
                    }
                ]
            }
        }
    },
    "metrics": {
        "namespace": "WebApp/$APPLICATION_NAME/$ENVIRONMENT",
        "metrics_collected": {
            "cpu": {
                "measurement": [
                    "cpu_usage_idle",
                    "cpu_usage_iowait",
                    "cpu_usage_user",
                    "cpu_usage_system"
                ],
                "metrics_collection_interval": 60
            },
            "disk": {
                "measurement": [
                    "used_percent"
                ],
                "metrics_collection_interval": 60,
                "resources": [
                    "*"
                ]
            },
            "mem": {
                "measurement": [
                    "mem_used_percent"
                ],
                "metrics_collection_interval": 60
            }
        }
    }
}
EOF

# Start CloudWatch agent with web configuration
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
    -a fetch-config \
    -m ec2 \
    -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent-web.json \
    -s

# Create a simple monitoring script
cat > /usr/local/bin/web-health-check.sh << 'EOF'
#!/bin/bash
# Web application health check script

# Check if Apache is running
if ! systemctl is-active --quiet httpd; then
    echo "ERROR: Apache is not running"
    exit 1
fi

# Check if health endpoint responds
if ! curl -f http://localhost/health > /dev/null 2>&1; then
    echo "ERROR: Health endpoint is not responding"
    exit 1
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 90 ]; then
    echo "WARNING: Disk usage is at $DISK_USAGE%"
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEM_USAGE" -gt 90 ]; then
    echo "WARNING: Memory usage is at $MEM_USAGE%"
fi

echo "Web application health check passed at $(date)"
EOF

chmod +x /usr/local/bin/web-health-check.sh

# Set up cron job for health checks
echo "*/5 * * * * root /usr/local/bin/web-health-check.sh >> /var/log/web-health-check.log 2>&1" >> /etc/crontab

# Create application deployment directory
mkdir -p /opt/webapp/releases
mkdir -p /opt/webapp/shared/logs
mkdir -p /opt/webapp/shared/uploads

# Set up log rotation for application logs
cat > /etc/logrotate.d/webapp << EOF
/opt/webapp/shared/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 apache apache
}

/var/log/web-health-check.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF

# Tag the instance
INSTANCE_ID=$(curl -s http://169.254.169.254/latest/meta-data/instance-id)
REGION=$(curl -s http://169.254.169.254/latest/meta-data/placement/region)

aws ec2 create-tags \
    --region "$REGION" \
    --resources "$INSTANCE_ID" \
    --tags \
        Key=WebServerConfigured,Value="$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
        Key=ApplicationReady,Value="true"

echo "Web application setup completed successfully at $(date)"
echo "Application is ready to serve traffic"

# Complete Web Application Example
# This example demonstrates how to use multiple refactored modules together
# to create a complete web application infrastructure

###############################################################
# Common Variables
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string
  default     = "webapp"
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string
  default     = "wa"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "region" {
  description = "AWS Region"
  type        = string
  default     = "us-east-1"
}

variable "region_abbreviated" {
  description = "Abbreviated region"
  type        = string
  default     = "ue1"
}

variable "build_number" {
  description = "Build number"
  type        = string
  default     = "1.0.0"
}

variable "launched_by" {
  description = "Who launched this infrastructure"
  type        = string
  default     = "terraform"
}

variable "launched_on" {
  description = "When this was launched"
  type        = string
  default     = "2024-01-01T00:00:00Z"
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
  default     = "+ops-team"
}

variable "vpc_name" {
  description = "Name of the VPC to use"
  type        = string
  default     = "main-vpc"
}

variable "ssl_certificate_arn" {
  description = "ARN of the SSL certificate"
  type        = string
}

variable "ec2_key_name" {
  description = "EC2 Key Pair name"
  type        = string
}

###############################################################
# Networking Data Sources
###############################################################

module "networking" {
  source = "../foundation/networking/data-sources"

  # Required variables
  application = var.application
  environment = var.environment
  region      = var.region
  vpc_name    = var.vpc_name
}

###############################################################
# Security Groups
###############################################################

module "security_groups" {
  source = "../foundation/security/security-groups"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "web"
  component               = "security"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Network configuration
  vpc_id          = module.networking.vpc_id
  homenet_cidr    = "************/32"
  ais_cidr        = "***********/32"
  remote_cidr     = "**************/32"
  nfs_cidr        = "10.0.0.0/8"
  ground_dc_cidrs = ["************/32", "**************/32"]

  # Security groups configuration
  security_groups = {
    load-balancer = {
      description = "Security group for application load balancer"
      ingress_rules = [
        {
          description = "HTTPS from internet"
          from_port   = 443
          to_port     = 443
          protocol    = "tcp"
          cidr_blocks = ["0.0.0.0/0"]
        },
        {
          description = "HTTP from internet (redirect to HTTPS)"
          from_port   = 80
          to_port     = 80
          protocol    = "tcp"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }

    web-server = {
      description = "Security group for web servers"
      ingress_rules = [
        {
          description     = "HTTP from load balancer"
          from_port       = 80
          to_port         = 80
          protocol        = "tcp"
          security_groups = [] # Will be populated after ALB creation
        },
        {
          description = "SSH from trusted networks"
          from_port   = 22
          to_port     = 22
          protocol    = "tcp"
          cidr_blocks = ["************/32", "***********/32"]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }

    database = {
      description = "Security group for database"
      ingress_rules = [
        {
          description = "MySQL from web servers"
          from_port   = 3306
          to_port     = 3306
          protocol    = "tcp"
          cidr_blocks = ["10.0.0.0/8"]
        },
        {
          description = "MySQL from trusted networks"
          from_port   = 3306
          to_port     = 3306
          protocol    = "tcp"
          cidr_blocks = ["************/32", "***********/32"]
        }
      ]
      egress_rules = [
        {
          description = "All outbound traffic"
          from_port   = 0
          to_port     = 0
          protocol    = "-1"
          cidr_blocks = ["0.0.0.0/0"]
        }
      ]
    }
  }
}

###############################################################
# Database
###############################################################

module "database" {
  source = "../storage/databases/aurora-mysql"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "database"
  component               = "webapp-db"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Database configuration
  database_name   = "webapp"
  master_username = "admin"
  
  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["database"]]

  # Instance configuration
  instance_class = "db.r6g.large"
  replica_count  = 2

  # Security
  storage_encrypted               = true
  deletion_protection            = true
  manage_master_user_password    = true

  # Monitoring
  performance_insights_enabled = true
  monitoring_interval         = 60
  enabled_cloudwatch_logs_exports = ["audit", "error", "general", "slowquery"]

  # Backup
  backup_retention_period = 30
  skip_final_snapshot    = false
}

###############################################################
# Application Load Balancer
###############################################################

module "load_balancer" {
  source = "../networking/load-balancer/alb"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "web"
  component               = "frontend"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.public_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["load-balancer"]]

  # Load balancer configuration
  internal                   = false
  enable_deletion_protection = true
  enable_access_logs        = true
  certificate_arn           = var.ssl_certificate_arn

  # Target groups
  target_groups = {
    web-servers = {
      port     = 80
      protocol = "HTTP"
      health_check = {
        path                = "/health"
        healthy_threshold   = 2
        unhealthy_threshold = 3
        timeout             = 5
        interval            = 30
        matcher             = "200"
      }
      stickiness = {
        enabled = false
      }
    }
  }

  # Listeners
  listeners = {
    http = {
      port     = 80
      protocol = "HTTP"
      default_action = {
        type = "redirect"
        redirect = {
          port        = "443"
          protocol    = "HTTPS"
          status_code = "HTTP_301"
        }
      }
    }
    https = {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = var.ssl_certificate_arn
      default_action = {
        type             = "forward"
        target_group_arn = "web-servers"
      }
    }
  }
}

###############################################################
# Web Servers (Auto Scaling Group)
###############################################################

module "web_servers" {
  source = "../compute/ec2-asg"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "web"
  component               = "frontend"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["web-server"]]

  # Instance configuration
  instance_type = "t3.medium"
  key_name      = var.ec2_key_name
  
  # Auto Scaling configuration
  min_size         = 2
  max_size         = 6
  desired_capacity = 3

  # Load balancer integration
  target_group_arns = [module.load_balancer.target_group_arns["web-servers"]]
  health_check_type = "ELB"

  # Storage configuration
  root_volume_size      = 30
  root_volume_type      = "gp3"
  root_volume_encrypted = true

  # Monitoring
  enable_cloudwatch_agent = true
  enable_ssm_agent        = true
  enable_monitoring       = true

  # Scaling policies
  enable_scaling_policies         = true
  cpu_utilization_high_threshold  = 75
  cpu_utilization_low_threshold   = 25

  # Custom user data for web application
  user_data_script = templatefile("${path.module}/user-data-web.sh", {
    database_endpoint = module.database.cluster_endpoint
    application_name  = var.application
    environment      = var.environment
  })

  depends_on = [module.database, module.load_balancer]
}

###############################################################
# Outputs
###############################################################

output "application_url" {
  description = "URL of the web application"
  value       = "https://${module.load_balancer.dns_name}"
}

output "database_endpoint" {
  description = "Database cluster endpoint"
  value       = module.database.cluster_endpoint
  sensitive   = true
}

output "load_balancer_dns" {
  description = "Load balancer DNS name"
  value       = module.load_balancer.dns_name
}

output "auto_scaling_group_name" {
  description = "Auto Scaling Group name"
  value       = module.web_servers.autoscaling_group_name
}

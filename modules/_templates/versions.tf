# Terraform and Provider Version Constraints Template
# This file should be copied to each module directory

terraform {
  required_version = ">= 1.5.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    
    # Add other providers as needed
    # template = {
    #   source  = "hashicorp/template"
    #   version = "~> 2.2"
    # }
    
    # random = {
    #   source  = "hashicorp/random"
    #   version = "~> 3.1"
    # }
    
    # null = {
    #   source  = "hashicorp/null"
    #   version = "~> 3.1"
    # }
  }
}

# Variables Template for Terraform Modules
# This template provides a standardized structure for module variables

###############################################################
# Required Pass-through Variables (Common across all modules)
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z0-9]{2,5}$", var.application_abbreviated))
    error_message = "Application abbreviation must be 2-5 characters, lowercase letters and numbers only."
  }
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string
  
  validation {
    condition = contains([
      "web", "api", "database", "processing", "monitoring", "security"
    ], var.service)
    error_message = "Service must be one of: web, api, database, processing, monitoring, security."
  }
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string
  
  validation {
    condition     = length(var.component) > 0 && can(regex("^[a-z0-9-]+$", var.component))
    error_message = "Component name must be non-empty and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Name of the environment"
  type        = string
  
  validation {
    condition = contains([
      "production", "nonprod", "homenet", "qamain", 
      "qarapid", "scratch", "uat"
    ], var.environment)
    error_message = "Environment must be one of: production, nonprod, homenet, qamain, qarapid, scratch, uat."
  }
}

variable "region" {
  description = "AWS Region"
  type        = string
  
  validation {
    condition = contains([
      "us-east-1", "us-west-2"
    ], var.region)
    error_message = "Region must be one of the supported regions: us-east-1, us-west-2."
  }
}

variable "region_abbreviated" {
  description = "Abbreviated form of the AWS region"
  type        = string
  
  validation {
    condition = contains([
      "ue1", "uw2"
    ], var.region_abbreviated)
    error_message = "Region abbreviation must be one of: ue1, uw2."
  }
}

variable "build_number" {
  description = "Build Number for tracking deployments"
  type        = string
  
  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.build_number))
    error_message = "Build number must be in semantic version format (e.g., 1.0.0)."
  }
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string
  
  validation {
    condition     = length(var.launched_by) > 0
    error_message = "Launched by cannot be empty."
  }
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string
  
  validation {
    condition     = can(formatdate("RFC3339", var.launched_on))
    error_message = "Launched on must be a valid RFC3339 timestamp."
  }
}

variable "slack_contact" {
  description = "Slack channel for notifications"
  type        = string
  
  validation {
    condition     = can(regex("^[+#@][a-zA-Z0-9-_]+$", var.slack_contact))
    error_message = "Slack contact must start with +, #, or @ followed by valid channel/user name."
  }
}

###############################################################
# Common Resource Tags
###############################################################

locals {
  common_tags = {
    Application  = var.application
    Service      = var.service
    Component    = var.component
    Environment  = var.environment
    Region       = var.region
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    ManagedBy    = "terraform"
  }
}

###############################################################
# Module-Specific Variables
# Add your module-specific variables below this section
###############################################################

# Example module-specific variable:
# variable "instance_type" {
#   description = "EC2 instance type"
#   type        = string
#   default     = "t3.micro"
#   
#   validation {
#     condition = contains([
#       "t3.micro", "t3.small", "t3.medium", "t3.large",
#       "m5.large", "m5.xlarge", "m5.2xlarge"
#     ], var.instance_type)
#     error_message = "Instance type must be a valid EC2 instance type."
#   }
# }

# Outputs Template for Terraform Modules
# This template provides a standardized structure for module outputs

###############################################################
# Resource Identifiers
###############################################################

# Example outputs - customize based on your module's resources

# output "resource_id" {
#   description = "The ID of the primary resource created by this module"
#   value       = aws_resource.example.id
# }

# output "resource_arn" {
#   description = "The ARN of the primary resource created by this module"
#   value       = aws_resource.example.arn
# }

# output "resource_name" {
#   description = "The name of the primary resource created by this module"
#   value       = aws_resource.example.name
# }

###############################################################
# Network Information (for networking modules)
###############################################################

# output "vpc_id" {
#   description = "ID of the VPC"
#   value       = aws_vpc.main.id
# }

# output "subnet_ids" {
#   description = "List of subnet IDs"
#   value       = aws_subnet.main[*].id
# }

# output "security_group_id" {
#   description = "ID of the security group"
#   value       = aws_security_group.main.id
# }

###############################################################
# Endpoint Information (for services)
###############################################################

# output "endpoint" {
#   description = "Service endpoint URL"
#   value       = aws_service.main.endpoint
# }

# output "dns_name" {
#   description = "DNS name of the service"
#   value       = aws_service.main.dns_name
# }

###############################################################
# Configuration Information
###############################################################

# output "configuration" {
#   description = "Configuration details for the created resources"
#   value = {
#     environment = var.environment
#     region      = var.region
#     component   = var.component
#   }
# }

###############################################################
# Sensitive Outputs (use sparingly and mark as sensitive)
###############################################################

# output "sensitive_value" {
#   description = "Sensitive configuration value"
#   value       = aws_resource.example.sensitive_attribute
#   sensitive   = true
# }

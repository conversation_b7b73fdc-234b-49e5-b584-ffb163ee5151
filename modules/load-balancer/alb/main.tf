# Application Load Balancer Module
# This module creates an Application Load Balancer using the official Terraform AWS modules

###############################################################
# S3 Bucket for Access Logs (if enabled and bucket not provided)
###############################################################

resource "aws_s3_bucket" "access_logs" {
  count  = var.enable_access_logs && var.access_logs_bucket == "" ? 1 : 0
  bucket = "${local.lb_name}-access-logs-${var.region_abbreviated}"

  tags = merge(
    local.common_tags,
    {
      Name    = "s3-alb-logs-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type    = "S3Bucket"
      Purpose = "ALBAccessLogs"
    }
  )
}

resource "aws_s3_bucket_versioning" "access_logs" {
  count  = var.enable_access_logs && var.access_logs_bucket == "" ? 1 : 0
  bucket = aws_s3_bucket.access_logs[0].id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "access_logs" {
  count  = var.enable_access_logs && var.access_logs_bucket == "" ? 1 : 0
  bucket = aws_s3_bucket.access_logs[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "access_logs" {
  count  = var.enable_access_logs && var.access_logs_bucket == "" ? 1 : 0
  bucket = aws_s3_bucket.access_logs[0].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Get ELB service account for access logs
data "aws_elb_service_account" "main" {
  count = var.enable_access_logs ? 1 : 0
}

resource "aws_s3_bucket_policy" "access_logs" {
  count  = var.enable_access_logs && var.access_logs_bucket == "" ? 1 : 0
  bucket = aws_s3_bucket.access_logs[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = data.aws_elb_service_account.main[0].arn
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.access_logs[0].arn}/*"
      },
      {
        Effect = "Allow"
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.access_logs[0].arn}/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Effect = "Allow"
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.access_logs[0].arn
      }
    ]
  })
}

###############################################################
# Application Load Balancer using Official Module
###############################################################

module "alb" {
  source  = "terraform-aws-modules/alb/aws"
  version = "~> 9.0"

  name               = local.lb_name
  load_balancer_type = var.load_balancer_type
  vpc_id             = var.vpc_id
  subnets            = var.subnet_ids
  security_groups    = var.security_group_ids

  # Load balancer configuration
  internal                         = var.internal
  enable_deletion_protection       = var.enable_deletion_protection
  idle_timeout                    = var.idle_timeout
  enable_cross_zone_load_balancing = var.enable_cross_zone_load_balancing
  enable_http2                    = var.enable_http2
  ip_address_type                 = var.ip_address_type

  # Access logs configuration
  access_logs = var.enable_access_logs ? {
    bucket  = var.access_logs_bucket != "" ? var.access_logs_bucket : aws_s3_bucket.access_logs[0].id
    prefix  = var.access_logs_prefix != "" ? var.access_logs_prefix : local.lb_name
    enabled = true
  } : {}

  # Target groups
  target_groups = {
    for key, tg in var.target_groups : key => {
      name             = local.tg_names[key]
      protocol         = tg.protocol
      port             = tg.port
      target_type      = tg.target_type
      vpc_id           = tg.vpc_id != "" ? tg.vpc_id : var.vpc_id
      
      health_check = {
        enabled             = tg.health_check.enabled
        healthy_threshold   = tg.health_check.healthy_threshold
        unhealthy_threshold = tg.health_check.unhealthy_threshold
        timeout             = tg.health_check.timeout
        interval            = tg.health_check.interval
        path                = tg.health_check.path
        matcher             = tg.health_check.matcher
        port                = tg.health_check.port
        protocol            = tg.health_check.protocol
      }

      stickiness = tg.stickiness.enabled ? {
        enabled         = tg.stickiness.enabled
        type           = tg.stickiness.type
        cookie_duration = tg.stickiness.cookie_duration
      } : {}

      tags = merge(
        local.common_tags,
        tg.tags,
        {
          Name = "tg-${key}-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
          Type = "TargetGroup"
        }
      )
    }
  }

  # Listeners
  listeners = {
    for key, listener in var.listeners : key => {
      port     = listener.port
      protocol = listener.protocol
      ssl_policy = listener.ssl_policy != "" ? listener.ssl_policy : (
        listener.protocol == "HTTPS" ? var.ssl_policy : null
      )
      certificate_arn = listener.certificate_arn != "" ? listener.certificate_arn : (
        listener.protocol == "HTTPS" ? var.certificate_arn : null
      )

      # Default action
      action_type = listener.default_action.type
      target_group_arn = listener.default_action.target_group_arn

      # Redirect action
      redirect = listener.default_action.redirect != null ? {
        port        = listener.default_action.redirect.port
        protocol    = listener.default_action.redirect.protocol
        status_code = listener.default_action.redirect.status_code
      } : null

      # Fixed response action
      fixed_response = listener.default_action.fixed_response != null ? {
        content_type = listener.default_action.fixed_response.content_type
        message_body = listener.default_action.fixed_response.message_body
        status_code  = listener.default_action.fixed_response.status_code
      } : null

      # Rules
      rules = {
        for idx, rule in listener.rules : "rule-${idx}" => {
          priority = rule.priority
          
          conditions = [
            for condition in rule.conditions : {
              field  = condition.field
              values = condition.values
            }
          ]

          actions = [
            for action in rule.actions : {
              type             = action.type
              target_group_arn = action.target_group_arn
              
              redirect = action.redirect != null ? {
                port        = action.redirect.port
                protocol    = action.redirect.protocol
                status_code = action.redirect.status_code
              } : null

              fixed_response = action.fixed_response != null ? {
                content_type = action.fixed_response.content_type
                message_body = action.fixed_response.message_body
                status_code  = action.fixed_response.status_code
              } : null
            }
          ]
        }
      }
    }
  }

  # Tags
  tags = local.common_tags

  depends_on = [
    aws_s3_bucket.access_logs,
    aws_s3_bucket_policy.access_logs
  ]
}

###############################################################
# WAF Association
###############################################################

resource "aws_wafv2_web_acl_association" "main" {
  count        = var.enable_waf && var.waf_acl_arn != "" ? 1 : 0
  resource_arn = module.alb.arn
  web_acl_arn  = var.waf_acl_arn

  depends_on = [module.alb]
}

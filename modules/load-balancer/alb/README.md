# Application Load Balancer (ALB) Module

This module creates an Application Load Balancer using the official Terraform AWS modules with enhanced security, monitoring, and best practices. It provides a standardized way to deploy ALBs with target groups, listeners, and SSL termination.

## Features

- **Official Module Integration**: Uses the official `terraform-aws-modules/alb/aws` module
- **Flexible Configuration**: Support for multiple target groups and listeners
- **SSL/TLS Termination**: HTTPS listeners with configurable SSL policies
- **Access Logging**: Automatic S3 bucket creation for access logs
- **WAF Integration**: Optional AWS WAF association
- **Health Checks**: Configurable health check parameters
- **Session Stickiness**: Support for sticky sessions
- **Cross-Zone Load Balancing**: Enhanced availability across AZs
- **Comprehensive Monitoring**: CloudWatch metrics and alarms support

## Usage

### Basic Web Application Example

```hcl
module "web_alb" {
  source = "../../modules/load-balancer/alb"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "web"
  component               = "frontend"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.public_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["load-balancer"]]

  # Load balancer configuration
  internal                   = false
  enable_deletion_protection = true
  enable_access_logs        = true
  certificate_arn           = var.ssl_certificate_arn

  # Target groups
  target_groups = {
    web-servers = {
      port     = 80
      protocol = "HTTP"
      health_check = {
        path                = "/health"
        healthy_threshold   = 2
        unhealthy_threshold = 3
        timeout             = 5
        interval            = 30
        matcher             = "200"
      }
      stickiness = {
        enabled = false
      }
    }
  }

  # Listeners
  listeners = {
    http = {
      port     = 80
      protocol = "HTTP"
      default_action = {
        type = "redirect"
        redirect = {
          port        = "443"
          protocol    = "HTTPS"
          status_code = "HTTP_301"
        }
      }
    }
    https = {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = var.ssl_certificate_arn
      default_action = {
        type             = "forward"
        target_group_arn = "web-servers"
      }
    }
  }
}
```

### API Gateway with Multiple Services

```hcl
module "api_alb" {
  source = "../../modules/load-balancer/alb"

  # ... required variables ...

  # Internal load balancer for API services
  internal = true

  target_groups = {
    api-v1 = {
      port     = 8080
      protocol = "HTTP"
      health_check = {
        path     = "/api/v1/health"
        matcher  = "200,202"
        interval = 15
      }
    }
    api-v2 = {
      port     = 8081
      protocol = "HTTP"
      health_check = {
        path     = "/api/v2/health"
        matcher  = "200"
        interval = 15
      }
    }
    admin = {
      port     = 9090
      protocol = "HTTP"
      health_check = {
        path = "/admin/health"
      }
    }
  }

  listeners = {
    api = {
      port     = 443
      protocol = "HTTPS"
      default_action = {
        type             = "forward"
        target_group_arn = "api-v2"
      }
      rules = [
        {
          priority = 100
          conditions = [
            {
              field  = "path-pattern"
              values = ["/api/v1/*"]
            }
          ]
          actions = [
            {
              type             = "forward"
              target_group_arn = "api-v1"
            }
          ]
        },
        {
          priority = 200
          conditions = [
            {
              field  = "path-pattern"
              values = ["/admin/*"]
            }
          ]
          actions = [
            {
              type             = "forward"
              target_group_arn = "admin"
            }
          ]
        }
      ]
    }
  }
}
```

### Blue-Green Deployment Example

```hcl
module "blue_green_alb" {
  source = "../../modules/load-balancer/alb"

  # ... required variables ...

  target_groups = {
    blue = {
      port     = 80
      protocol = "HTTP"
      health_check = {
        path = "/health"
      }
      tags = {
        Deployment = "blue"
      }
    }
    green = {
      port     = 80
      protocol = "HTTP"
      health_check = {
        path = "/health"
      }
      tags = {
        Deployment = "green"
      }
    }
  }

  listeners = {
    production = {
      port     = 443
      protocol = "HTTPS"
      default_action = {
        type             = "forward"
        target_group_arn = "blue"  # Switch to "green" for deployment
      }
    }
    staging = {
      port     = 8443
      protocol = "HTTPS"
      default_action = {
        type             = "forward"
        target_group_arn = "green"  # Always points to staging environment
      }
    }
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.5.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| alb | terraform-aws-modules/alb/aws | ~> 9.0 |

## Resources

- `aws_s3_bucket` - Access logs bucket (optional)
- `aws_s3_bucket_*` - S3 bucket configuration resources
- `aws_wafv2_web_acl_association` - WAF association (optional)

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| application | Name of the application | `string` | n/a | yes |
| environment | Name of the environment | `string` | n/a | yes |
| vpc_id | VPC ID | `string` | n/a | yes |
| subnet_ids | List of subnet IDs | `list(string)` | n/a | yes |
| target_groups | Map of target groups | `map(object)` | `{}` | no |
| listeners | Map of listeners | `map(object)` | `{}` | no |
| internal | Whether LB is internal | `bool` | `false` | no |
| enable_deletion_protection | Enable deletion protection | `bool` | `true` | no |
| certificate_arn | SSL certificate ARN | `string` | `""` | no |

## Outputs

| Name | Description |
|------|-------------|
| dns_name | Load balancer DNS name |
| zone_id | Load balancer hosted zone ID |
| arn | Load balancer ARN |
| target_group_arns | Map of target group ARNs |
| listener_arns | Map of listener ARNs |
| connection_info | Connection information summary |

## Security Features

### SSL/TLS Configuration
- **HTTPS Listeners**: Support for SSL termination
- **SSL Policies**: Configurable security policies
- **Certificate Management**: ACM certificate integration

### Access Control
- **Security Groups**: Network-level access control
- **WAF Integration**: Application-level protection
- **Internal/External**: Flexible deployment options

### Logging and Monitoring
- **Access Logs**: Detailed request logging to S3
- **CloudWatch Metrics**: Comprehensive monitoring
- **Health Checks**: Application health monitoring

## Best Practices Implemented

1. **High Availability**: Multi-AZ deployment
2. **Security**: HTTPS by default, WAF support
3. **Monitoring**: Access logs and CloudWatch integration
4. **Performance**: Cross-zone load balancing
5. **Compliance**: Secure S3 bucket configuration
6. **Cost Optimization**: Efficient resource allocation

## Migration from Existing ALBs

### From Manual ALB Configuration

```hcl
# Old manual configuration
resource "aws_lb" "old_alb" {
  name               = "old-alb"
  load_balancer_type = "application"
  # ... configuration ...
}

# New module-based configuration
module "new_alb" {
  source = "../../modules/load-balancer/alb"
  # ... configuration ...
}

# Update references
# Old: aws_lb.old_alb.dns_name
# New: module.new_alb.dns_name
```

## Monitoring and Alerting

### Key Metrics to Monitor
- **RequestCount**: Number of requests
- **TargetResponseTime**: Response latency
- **HTTPCode_Target_5XX_Count**: Server errors
- **UnHealthyHostCount**: Unhealthy targets
- **ActiveConnectionCount**: Active connections

### Recommended Alarms
- High error rate (5XX responses)
- High response time
- Unhealthy target count
- Low healthy target count

## Troubleshooting

### Common Issues

1. **Target Registration Failures**
   - Check security group rules
   - Verify health check configuration
   - Ensure targets are in correct subnets

2. **SSL Certificate Issues**
   - Verify certificate ARN
   - Check certificate validation
   - Ensure certificate covers all domains

3. **Access Log Issues**
   - Check S3 bucket permissions
   - Verify ELB service account access
   - Review bucket policy configuration

### Debugging Commands

```bash
# Check target health
aws elbv2 describe-target-health --target-group-arn <arn>

# View load balancer attributes
aws elbv2 describe-load-balancer-attributes --load-balancer-arn <arn>

# Check listener rules
aws elbv2 describe-rules --listener-arn <arn>
```

## Contributing

When contributing to this module:

1. Test with multiple target group configurations
2. Validate SSL/TLS configurations
3. Ensure access log functionality
4. Test WAF integration
5. Update documentation for new features

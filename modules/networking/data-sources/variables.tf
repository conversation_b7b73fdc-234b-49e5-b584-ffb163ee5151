# Networking Data Sources Module Variables

###############################################################
# Required Pass-through Variables
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "environment" {
  description = "Name of the environment"
  type        = string
  
  validation {
    condition = contains([
      "production", "nonprod", "homenet", "qamain", 
      "qarapid", "scratch", "uat"
    ], var.environment)
    error_message = "Environment must be one of: production, nonprod, homenet, qamain, qarapid, scratch, uat."
  }
}

variable "region" {
  description = "AWS Region"
  type        = string
  
  validation {
    condition = contains([
      "us-east-1", "us-west-2"
    ], var.region)
    error_message = "Region must be one of the supported regions: us-east-1, us-west-2."
  }
}

###############################################################
# VPC Configuration Variables
###############################################################

variable "vpc_name" {
  description = "Name of the VPC to look up"
  type        = string
  
  validation {
    condition     = length(var.vpc_name) > 0
    error_message = "VPC name cannot be empty."
  }
}

variable "rr_vpc_name" {
  description = "Name of the Rates & Residuals VPC to look up"
  type        = string
  default     = ""
}

###############################################################
# Subnet Configuration Variables
###############################################################

variable "public_subnet_tag_key" {
  description = "Tag key to identify public subnets"
  type        = string
  default     = "SUB-Type"
}

variable "public_subnet_tag_value" {
  description = "Tag value to identify public subnets"
  type        = string
  default     = "Public"
}

variable "private_subnet_tag_key" {
  description = "Tag key to identify private subnets"
  type        = string
  default     = "SUB-Type"
}

variable "private_subnet_tag_value" {
  description = "Tag value to identify private subnets"
  type        = string
  default     = "Private"
}

###############################################################
# Additional Filters
###############################################################

variable "additional_vpc_filters" {
  description = "Additional filters for VPC lookup"
  type = map(object({
    name   = string
    values = list(string)
  }))
  default = {}
}

variable "additional_subnet_filters" {
  description = "Additional filters for subnet lookup"
  type = map(object({
    name   = string
    values = list(string)
  }))
  default = {}
}

###############################################################
# Common Resource Tags
###############################################################

locals {
  common_tags = {
    Application = var.application
    Environment = var.environment
    Region      = var.region
    ManagedBy   = "terraform"
    Module      = "networking-data-sources"
  }
}

# Networking Data Sources Module Outputs

###############################################################
# VPC Outputs
###############################################################

output "vpc_id" {
  description = "ID of the main VPC"
  value       = data.aws_vpc.main.id
}

output "vpc_arn" {
  description = "ARN of the main VPC"
  value       = data.aws_vpc.main.arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the main VPC"
  value       = data.aws_vpc.main.cidr_block
}

output "vpc_name" {
  description = "Name of the main VPC"
  value       = var.vpc_name
}

output "rates_residuals_vpc_id" {
  description = "ID of the Rates & Residuals VPC (if configured)"
  value       = var.rr_vpc_name != "" ? data.aws_vpc.rates_residuals[0].id : null
}

output "rates_residuals_vpc_arn" {
  description = "ARN of the Rates & Residuals VPC (if configured)"
  value       = var.rr_vpc_name != "" ? data.aws_vpc.rates_residuals[0].arn : null
}

###############################################################
# Subnet Outputs
###############################################################

output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = data.aws_subnets.public.ids
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = data.aws_subnets.private.ids
}

output "public_subnet_count" {
  description = "Number of public subnets"
  value       = length(data.aws_subnets.public.ids)
}

output "private_subnet_count" {
  description = "Number of private subnets"
  value       = length(data.aws_subnets.private.ids)
}

###############################################################
# Individual Subnet Details
###############################################################

output "private_primary_subnet" {
  description = "Details of the primary private subnet"
  value = length(data.aws_subnet.private_primary) > 0 ? {
    id                = data.aws_subnet.private_primary[0].id
    arn               = data.aws_subnet.private_primary[0].arn
    cidr_block        = data.aws_subnet.private_primary[0].cidr_block
    availability_zone = data.aws_subnet.private_primary[0].availability_zone
  } : null
}

output "private_secondary_subnet" {
  description = "Details of the secondary private subnet"
  value = length(data.aws_subnet.private_secondary) > 0 ? {
    id                = data.aws_subnet.private_secondary[0].id
    arn               = data.aws_subnet.private_secondary[0].arn
    cidr_block        = data.aws_subnet.private_secondary[0].cidr_block
    availability_zone = data.aws_subnet.private_secondary[0].availability_zone
  } : null
}

output "all_private_subnets" {
  description = "Details of all private subnets"
  value = [
    for subnet in data.aws_subnet.all_private : {
      id                = subnet.id
      arn               = subnet.arn
      cidr_block        = subnet.cidr_block
      availability_zone = subnet.availability_zone
    }
  ]
}

output "all_public_subnets" {
  description = "Details of all public subnets"
  value = [
    for subnet in data.aws_subnet.all_public : {
      id                = subnet.id
      arn               = subnet.arn
      cidr_block        = subnet.cidr_block
      availability_zone = subnet.availability_zone
    }
  ]
}

###############################################################
# Availability Zone Outputs
###############################################################

output "availability_zones" {
  description = "List of all availability zones used by subnets"
  value       = local.all_azs
}

output "private_availability_zones" {
  description = "List of availability zones for private subnets"
  value       = local.private_azs
}

output "public_availability_zones" {
  description = "List of availability zones for public subnets"
  value       = local.public_azs
}

###############################################################
# Route Table Outputs
###############################################################

output "private_route_table_ids" {
  description = "List of private route table IDs"
  value       = data.aws_route_tables.private.ids
}

output "public_route_table_ids" {
  description = "List of public route table IDs"
  value       = data.aws_route_tables.public.ids
}

###############################################################
# Configuration Summary
###############################################################

output "network_configuration" {
  description = "Summary of network configuration"
  value = {
    vpc_id                = data.aws_vpc.main.id
    vpc_cidr              = data.aws_vpc.main.cidr_block
    public_subnet_count   = length(data.aws_subnets.public.ids)
    private_subnet_count  = length(data.aws_subnets.private.ids)
    availability_zones    = local.all_azs
    has_rr_vpc           = var.rr_vpc_name != ""
  }
}

# Networking Data Sources Module
# This module provides standardized data source lookups for networking resources

###############################################################
# VPC Data Sources
###############################################################

# Primary VPC lookup
data "aws_vpc" "main" {
  filter {
    name   = "tag:Name"
    values = [var.vpc_name]
  }

  # Apply additional filters if provided
  dynamic "filter" {
    for_each = var.additional_vpc_filters
    content {
      name   = filter.value.name
      values = filter.value.values
    }
  }

  tags = local.common_tags
}

# Rates & Residuals VPC lookup (optional)
data "aws_vpc" "rates_residuals" {
  count = var.rr_vpc_name != "" ? 1 : 0
  
  filter {
    name   = "tag:Name"
    values = [var.rr_vpc_name]
  }

  # Apply additional filters if provided
  dynamic "filter" {
    for_each = var.additional_vpc_filters
    content {
      name   = filter.value.name
      values = filter.value.values
    }
  }

  tags = local.common_tags
}

###############################################################
# Subnet Data Sources
###############################################################

# Public subnets lookup
data "aws_subnets" "public" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.main.id]
  }

  tags = {
    "${var.public_subnet_tag_key}" = var.public_subnet_tag_value
  }

  # Apply additional filters if provided
  dynamic "filter" {
    for_each = var.additional_subnet_filters
    content {
      name   = filter.value.name
      values = filter.value.values
    }
  }
}

# Private subnets lookup
data "aws_subnets" "private" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.main.id]
  }

  tags = {
    "${var.private_subnet_tag_key}" = var.private_subnet_tag_value
  }

  # Apply additional filters if provided
  dynamic "filter" {
    for_each = var.additional_subnet_filters
    content {
      name   = filter.value.name
      values = filter.value.values
    }
  }
}

###############################################################
# Individual Subnet Details
###############################################################

# Get details for the first two private subnets (commonly used for RDS)
data "aws_subnet" "private_primary" {
  count = length(data.aws_subnets.private.ids) > 0 ? 1 : 0
  id    = data.aws_subnets.private.ids[0]
}

data "aws_subnet" "private_secondary" {
  count = length(data.aws_subnets.private.ids) > 1 ? 1 : 0
  id    = data.aws_subnets.private.ids[1]
}

# Get details for all private subnets
data "aws_subnet" "all_private" {
  count = length(data.aws_subnets.private.ids)
  id    = data.aws_subnets.private.ids[count.index]
}

# Get details for all public subnets
data "aws_subnet" "all_public" {
  count = length(data.aws_subnets.public.ids)
  id    = data.aws_subnets.public.ids[count.index]
}

###############################################################
# Availability Zones
###############################################################

# Get availability zones for the subnets
locals {
  private_azs = [for subnet in data.aws_subnet.all_private : subnet.availability_zone]
  public_azs  = [for subnet in data.aws_subnet.all_public : subnet.availability_zone]
  all_azs     = distinct(concat(local.private_azs, local.public_azs))
}

###############################################################
# Route Tables (for advanced networking scenarios)
###############################################################

# Get route tables associated with private subnets
data "aws_route_tables" "private" {
  vpc_id = data.aws_vpc.main.id

  filter {
    name   = "association.subnet-id"
    values = data.aws_subnets.private.ids
  }
}

# Get route tables associated with public subnets
data "aws_route_tables" "public" {
  vpc_id = data.aws_vpc.main.id

  filter {
    name   = "association.subnet-id"
    values = data.aws_subnets.public.ids
  }
}

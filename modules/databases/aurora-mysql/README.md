# Aurora MySQL Database Module

This module creates an Amazon Aurora MySQL cluster using the official Terraform AWS modules with enhanced security, monitoring, and best practices. It provides a standardized way to deploy Aurora MySQL databases across environments.

## Features

- **Official Module Integration**: Uses the official `terraform-aws-modules/rds-aurora/aws` module
- **Enhanced Security**: Encryption at rest, managed passwords, VPC security groups
- **High Availability**: Multi-AZ deployment with automatic failover
- **Performance Monitoring**: Performance Insights, Enhanced Monitoring, CloudWatch logs
- **Automated Backups**: Configurable backup retention and maintenance windows
- **Auto Scaling**: Automatic read replica scaling based on demand
- **DNS Integration**: Optional Route53 DNS records for easy access
- **Parameter Groups**: Custom cluster and instance parameter groups
- **Comprehensive Tagging**: Consistent tagging across all resources

## Usage

### Basic Example

```hcl
module "aurora_mysql" {
  source = "../../modules/databases/aurora-mysql"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = "database"
  component               = "incentives"
  environment             = var.environment
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  build_number            = var.build_number
  launched_by             = var.launched_by
  launched_on             = var.launched_on
  slack_contact           = var.slack_contact

  # Database configuration
  database_name = "incentives"
  master_username = "admin"
  
  # Network configuration
  vpc_id             = module.networking.vpc_id
  subnet_ids         = module.networking.private_subnet_ids
  security_group_ids = [module.security_groups.security_group_ids["database"]]

  # Instance configuration
  instance_class = "db.r6g.large"
  replica_count  = 2

  # Security
  storage_encrypted = true
  deletion_protection = true
  manage_master_user_password = true

  # Monitoring
  performance_insights_enabled = true
  monitoring_interval = 60
  enabled_cloudwatch_logs_exports = ["audit", "error", "general", "slowquery"]

  # DNS
  create_dns_record = true
  internal_domain   = var.internal_domain
}
```

### Production Example with Custom Parameters

```hcl
module "aurora_mysql_prod" {
  source = "../../modules/databases/aurora-mysql"

  # ... required variables ...

  # Production configuration
  database_name = "production_db"
  instance_class = "db.r6g.xlarge"
  replica_count = 3

  # Enhanced backup configuration
  backup_retention_period = 30
  preferred_backup_window = "03:00-04:00"
  preferred_maintenance_window = "sun:04:00-sun:05:00"
  copy_tags_to_snapshot = true
  skip_final_snapshot = false

  # Performance optimization
  performance_insights_enabled = true
  performance_insights_retention_period = 31
  monitoring_interval = 15

  # Custom cluster parameters
  cluster_parameters = [
    {
      name  = "innodb_buffer_pool_size"
      value = "{DBInstanceClassMemory*3/4}"
    },
    {
      name  = "slow_query_log"
      value = "1"
    },
    {
      name  = "long_query_time"
      value = "2"
    }
  ]

  # Custom instance parameters
  instance_parameters = [
    {
      name  = "innodb_print_all_deadlocks"
      value = "1"
    }
  ]

  # Auto scaling configuration
  instances = {
    writer = {
      instance_class = "db.r6g.xlarge"
      publicly_accessible = false
    }
    reader1 = {
      identifier = "reader-1"
      instance_class = "db.r6g.large"
    }
    reader2 = {
      identifier = "reader-2" 
      instance_class = "db.r6g.large"
    }
  }
}
```

### Restore from Snapshot Example

```hcl
module "aurora_mysql_restore" {
  source = "../../modules/databases/aurora-mysql"

  # ... required variables ...

  # Restore configuration
  snapshot_identifier = "arn:aws:rds:us-east-1:123456789012:cluster-snapshot:my-snapshot"
  
  # Override database name for restored cluster
  database_name = "restored_db"
  
  # Use different identifier to avoid conflicts
  database_identifier_suffix = "restored"
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.5.0 |
| aws | ~> 5.0 |
| random | ~> 3.1 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |
| random | ~> 3.1 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| aurora | terraform-aws-modules/rds-aurora/aws | ~> 8.0 |

## Resources

- `aws_db_subnet_group` - Database subnet group
- `aws_rds_cluster_parameter_group` - Cluster parameter group (optional)
- `aws_db_parameter_group` - Instance parameter group (optional)
- `aws_cloudwatch_log_group` - CloudWatch log groups
- `aws_route53_record` - DNS records (optional)
- `random_password` - Master password (if not using managed passwords)

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| application | Name of the application | `string` | n/a | yes |
| environment | Name of the environment | `string` | n/a | yes |
| database_name | Name of the database | `string` | n/a | yes |
| vpc_id | VPC ID | `string` | n/a | yes |
| subnet_ids | List of subnet IDs | `list(string)` | n/a | yes |
| instance_class | Instance class | `string` | `"db.r6g.large"` | no |
| replica_count | Number of read replicas | `number` | `1` | no |
| storage_encrypted | Enable encryption | `bool` | `true` | no |
| manage_master_user_password | Use AWS managed passwords | `bool` | `true` | no |

## Outputs

| Name | Description |
|------|-------------|
| cluster_endpoint | Writer endpoint |
| cluster_reader_endpoint | Reader endpoint |
| cluster_id | Cluster identifier |
| cluster_arn | Cluster ARN |
| connection_info | Database connection information |
| database_configuration | Configuration summary |

## Security Features

### Encryption
- **Encryption at Rest**: Enabled by default using AWS managed keys
- **Custom KMS Keys**: Support for customer-managed KMS keys
- **Encryption in Transit**: SSL/TLS connections enforced

### Access Control
- **VPC Security Groups**: Network-level access control
- **Managed Passwords**: AWS Secrets Manager integration
- **IAM Database Authentication**: Optional IAM-based access

### Monitoring and Auditing
- **Performance Insights**: Query performance monitoring
- **Enhanced Monitoring**: OS-level metrics
- **CloudWatch Logs**: Audit, error, general, and slow query logs
- **CloudWatch Alarms**: Automated alerting (configured separately)

## Best Practices Implemented

1. **High Availability**: Multi-AZ deployment across multiple subnets
2. **Automated Backups**: 30-day retention with point-in-time recovery
3. **Maintenance Windows**: Scheduled during low-traffic periods
4. **Parameter Optimization**: Tuned for Aurora MySQL performance
5. **Monitoring**: Comprehensive monitoring and logging
6. **Security**: Encryption, VPC isolation, managed passwords
7. **Tagging**: Consistent resource tagging for management

## Migration from Existing Databases

### From Current Incentives Module

```hcl
# Old module reference
module "incentives_db" {
  source = "../../modules/databases/incentives"
  # ... configuration ...
}

# New module reference
module "incentives_aurora" {
  source = "../../modules/databases/aurora-mysql"
  
  database_name = "incentives"
  component     = "incentives"
  # ... other configuration ...
}

# Update references
# Old: module.incentives_db.rds_cluster_endpoint
# New: module.incentives_aurora.cluster_endpoint
```

## Troubleshooting

### Common Issues

1. **Subnet Group Errors**
   - Ensure subnets are in different AZs
   - Verify subnet IDs are correct

2. **Parameter Group Conflicts**
   - Check parameter compatibility with engine version
   - Validate parameter values

3. **Security Group Access**
   - Verify security group rules allow database access
   - Check CIDR blocks and port configurations

### Monitoring and Alerts

- Monitor cluster CPU, memory, and connection metrics
- Set up CloudWatch alarms for critical thresholds
- Review Performance Insights for query optimization
- Monitor backup success and duration

## Cost Optimization

- Use appropriate instance classes for workload
- Configure auto scaling for read replicas
- Optimize backup retention periods
- Consider Reserved Instances for production

## Contributing

When contributing to this module:

1. Test with multiple Aurora MySQL versions
2. Validate parameter group configurations
3. Ensure backward compatibility
4. Update documentation for new features
5. Follow security best practices

# Aurora MySQL Database Module Outputs

###############################################################
# Cluster Information
###############################################################

output "cluster_arn" {
  description = "Amazon Resource Name (ARN) of cluster"
  value       = module.aurora.cluster_arn
}

output "cluster_id" {
  description = "The RDS Cluster Identifier"
  value       = module.aurora.cluster_id
}

output "cluster_identifier" {
  description = "The RDS Cluster Identifier"
  value       = module.aurora.cluster_identifier
}

output "cluster_resource_id" {
  description = "The RDS Cluster Resource ID"
  value       = module.aurora.cluster_resource_id
}

output "cluster_members" {
  description = "List of RDS Instances that are a part of this cluster"
  value       = module.aurora.cluster_members
}

###############################################################
# Endpoints
###############################################################

output "cluster_endpoint" {
  description = "Writer endpoint for the cluster"
  value       = module.aurora.cluster_endpoint
}

output "cluster_reader_endpoint" {
  description = "A read-only endpoint for the cluster"
  value       = module.aurora.cluster_reader_endpoint
}

output "cluster_engine_version_actual" {
  description = "The running version of the cluster database"
  value       = module.aurora.cluster_engine_version_actual
}

output "cluster_database_name" {
  description = "Name for an automatically created database on cluster creation"
  value       = module.aurora.cluster_database_name
}

output "cluster_port" {
  description = "The database port"
  value       = module.aurora.cluster_port
}

###############################################################
# Instance Information
###############################################################

output "cluster_instances" {
  description = "A map of cluster instances and their attributes"
  value       = module.aurora.cluster_instances
}

output "additional_cluster_endpoints" {
  description = "A map of additional cluster endpoints and their attributes"
  value       = module.aurora.additional_cluster_endpoints
}

###############################################################
# Security and Access
###############################################################

output "cluster_master_username" {
  description = "The database master username"
  value       = module.aurora.cluster_master_username
  sensitive   = true
}

output "cluster_master_user_secret" {
  description = "The generated database master user secret when manage_master_user_password is set to true"
  value       = module.aurora.cluster_master_user_secret
  sensitive   = true
}

output "cluster_hosted_zone_id" {
  description = "The Route53 Hosted Zone ID of the endpoint"
  value       = module.aurora.cluster_hosted_zone_id
}

###############################################################
# Subnet Group
###############################################################

output "db_subnet_group_name" {
  description = "The db subnet group name"
  value       = aws_db_subnet_group.this.name
}

output "db_subnet_group_id" {
  description = "The db subnet group ID"
  value       = aws_db_subnet_group.this.id
}

output "db_subnet_group_arn" {
  description = "The ARN of the db subnet group"
  value       = aws_db_subnet_group.this.arn
}

###############################################################
# Parameter Groups
###############################################################

output "db_cluster_parameter_group_arn" {
  description = "The ARN of the DB cluster parameter group"
  value       = length(aws_rds_cluster_parameter_group.this) > 0 ? aws_rds_cluster_parameter_group.this[0].arn : null
}

output "db_cluster_parameter_group_id" {
  description = "The ID of the DB cluster parameter group"
  value       = length(aws_rds_cluster_parameter_group.this) > 0 ? aws_rds_cluster_parameter_group.this[0].id : null
}

output "db_parameter_group_arn" {
  description = "The ARN of the DB parameter group"
  value       = length(aws_db_parameter_group.this) > 0 ? aws_db_parameter_group.this[0].arn : null
}

output "db_parameter_group_id" {
  description = "The ID of the DB parameter group"
  value       = length(aws_db_parameter_group.this) > 0 ? aws_db_parameter_group.this[0].id : null
}

###############################################################
# CloudWatch Log Groups
###############################################################

output "cloudwatch_log_groups" {
  description = "Map of CloudWatch log groups created for the cluster"
  value = {
    for log_type, log_group in aws_cloudwatch_log_group.this : log_type => {
      name = log_group.name
      arn  = log_group.arn
    }
  }
}

###############################################################
# DNS Records
###############################################################

output "dns_records" {
  description = "DNS records created for the cluster"
  value = var.create_dns_record ? {
    writer_fqdn = aws_route53_record.cluster_writer[0].fqdn
    reader_fqdn = aws_route53_record.cluster_reader[0].fqdn
    writer_name = aws_route53_record.cluster_writer[0].name
    reader_name = aws_route53_record.cluster_reader[0].name
  } : null
}

###############################################################
# Enhanced Monitoring
###############################################################

output "enhanced_monitoring_iam_role_name" {
  description = "The name of the monitoring role"
  value       = module.aurora.enhanced_monitoring_iam_role_name
}

output "enhanced_monitoring_iam_role_arn" {
  description = "The Amazon Resource Name (ARN) specifying the monitoring role"
  value       = module.aurora.enhanced_monitoring_iam_role_arn
}

output "enhanced_monitoring_iam_role_unique_id" {
  description = "Stable and unique string identifying the monitoring role"
  value       = module.aurora.enhanced_monitoring_iam_role_unique_id
}

###############################################################
# Security Group
###############################################################

output "security_group_id" {
  description = "The security group ID of the cluster"
  value       = module.aurora.security_group_id
}

###############################################################
# Configuration Summary
###############################################################

output "database_configuration" {
  description = "Summary of database configuration"
  value = {
    cluster_identifier    = local.cluster_identifier
    engine               = "aurora-mysql"
    engine_version       = var.engine_version
    database_name        = var.database_name
    instance_class       = var.instance_class
    replica_count        = var.replica_count
    storage_encrypted    = var.storage_encrypted
    deletion_protection  = var.deletion_protection
    backup_retention     = var.backup_retention_period
    multi_az            = length(var.subnet_ids) > 1
    performance_insights = var.performance_insights_enabled
    monitoring_interval  = var.monitoring_interval
  }
}

###############################################################
# Connection Information
###############################################################

output "connection_info" {
  description = "Database connection information"
  value = {
    writer_endpoint = module.aurora.cluster_endpoint
    reader_endpoint = module.aurora.cluster_reader_endpoint
    port           = module.aurora.cluster_port
    database_name  = module.aurora.cluster_database_name
    username       = module.aurora.cluster_master_username
    # Note: Password is not included for security reasons
    # Use cluster_master_user_secret output for managed passwords
  }
  sensitive = true
}

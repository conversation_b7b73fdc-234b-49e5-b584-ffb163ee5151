# Lambda Function Module Outputs

###############################################################
# Lambda Function Information
###############################################################

output "lambda_function_arn" {
  description = "The ARN of the Lambda Function"
  value       = module.lambda_function.lambda_function_arn
}

output "lambda_function_name" {
  description = "The name of the Lambda Function"
  value       = module.lambda_function.lambda_function_name
}

output "lambda_function_qualified_arn" {
  description = "The ARN identifying your Lambda Function Version"
  value       = module.lambda_function.lambda_function_qualified_arn
}

output "lambda_function_version" {
  description = "Latest published version of Lambda Function"
  value       = module.lambda_function.lambda_function_version
}

output "lambda_function_last_modified" {
  description = "The date Lambda Function resource was last modified"
  value       = module.lambda_function.lambda_function_last_modified
}

output "lambda_function_kms_key_arn" {
  description = "The ARN for the KMS encryption key of Lambda Function"
  value       = module.lambda_function.lambda_function_kms_key_arn
}

output "lambda_function_source_code_hash" {
  description = "Base64-encoded representation of raw SHA-256 sum of the zip file"
  value       = module.lambda_function.lambda_function_source_code_hash
}

output "lambda_function_source_code_size" {
  description = "The size in bytes of the function .zip file"
  value       = module.lambda_function.lambda_function_source_code_size
}

output "lambda_function_invoke_arn" {
  description = "The Invoke ARN of the Lambda Function"
  value       = module.lambda_function.lambda_function_invoke_arn
}

output "lambda_function_url" {
  description = "The Lambda Function URL"
  value       = module.lambda_function.lambda_function_url
}

output "lambda_function_url_creation_config" {
  description = "The Lambda Function URL creation config"
  value       = module.lambda_function.lambda_function_url_creation_config
}

###############################################################
# IAM Information
###############################################################

output "lambda_role_arn" {
  description = "The ARN of the IAM role created for the Lambda Function"
  value       = module.lambda_function.lambda_role_arn
}

output "lambda_role_name" {
  description = "The name of the IAM role created for the Lambda Function"
  value       = module.lambda_function.lambda_role_name
}

output "lambda_role_unique_id" {
  description = "The unique id of the IAM role created for the Lambda Function"
  value       = module.lambda_function.lambda_role_unique_id
}

###############################################################
# CloudWatch Log Group
###############################################################

output "lambda_cloudwatch_log_group_arn" {
  description = "The ARN of the Cloudwatch Log Group"
  value       = module.lambda_function.lambda_cloudwatch_log_group_arn
}

output "lambda_cloudwatch_log_group_name" {
  description = "The name of the Cloudwatch Log Group"
  value       = module.lambda_function.lambda_cloudwatch_log_group_name
}

###############################################################
# Event Source Mapping
###############################################################

output "lambda_event_source_mapping_function_arn" {
  description = "The the ARN of the Lambda function the event source mapping is sending events to"
  value = {
    for key, mapping in aws_lambda_event_source_mapping.this : key => mapping.function_arn
  }
}

output "lambda_event_source_mapping_state" {
  description = "The state of the event source mapping"
  value = {
    for key, mapping in aws_lambda_event_source_mapping.this : key => mapping.state
  }
}

output "lambda_event_source_mapping_state_transition_reason" {
  description = "The reason the event source mapping is in its current state"
  value = {
    for key, mapping in aws_lambda_event_source_mapping.this : key => mapping.state_transition_reason
  }
}

output "lambda_event_source_mapping_uuid" {
  description = "The UUID of the created event source mapping"
  value = {
    for key, mapping in aws_lambda_event_source_mapping.this : key => mapping.uuid
  }
}

###############################################################
# Provisioned Concurrency
###############################################################

output "lambda_provisioned_concurrency_config_arn" {
  description = "The ARN of the provisioned concurrency configuration"
  value       = var.provisioned_concurrency_config != null ? aws_lambda_provisioned_concurrency_config.this[0].arn : null
}

output "lambda_provisioned_concurrency_config_allocated_capacity" {
  description = "The amount of provisioned concurrency allocated"
  value       = var.provisioned_concurrency_config != null ? aws_lambda_provisioned_concurrency_config.this[0].allocated_capacity : null
}

output "lambda_provisioned_concurrency_config_available_capacity" {
  description = "The amount of provisioned concurrency available"
  value       = var.provisioned_concurrency_config != null ? aws_lambda_provisioned_concurrency_config.this[0].available_capacity : null
}

###############################################################
# CloudWatch Alarms
###############################################################

output "cloudwatch_alarms" {
  description = "Map of CloudWatch alarms created for the Lambda function"
  value = {
    error_rate = aws_cloudwatch_metric_alarm.error_rate.arn
    duration   = aws_cloudwatch_metric_alarm.duration.arn
    throttles  = aws_cloudwatch_metric_alarm.throttles.arn
  }
}

output "cloudwatch_alarm_names" {
  description = "Map of CloudWatch alarm names"
  value = {
    error_rate = aws_cloudwatch_metric_alarm.error_rate.alarm_name
    duration   = aws_cloudwatch_metric_alarm.duration.alarm_name
    throttles  = aws_cloudwatch_metric_alarm.throttles.alarm_name
  }
}

###############################################################
# Configuration Summary
###############################################################

output "lambda_configuration" {
  description = "Summary of Lambda function configuration"
  value = {
    function_name    = local.function_name
    runtime         = var.runtime
    handler         = var.handler
    timeout         = var.timeout
    memory_size     = var.memory_size
    package_type    = var.package_type
    architectures   = var.architectures
    vpc_configured  = var.vpc_config != null
    layers_count    = length(var.layers)
    environment_vars_count = length(var.environment_variables)
    tracing_mode    = var.tracing_config_mode
    reserved_concurrency = var.reserved_concurrent_executions
    provisioned_concurrency = var.provisioned_concurrency_config != null ? var.provisioned_concurrency_config.provisioned_concurrent_executions : 0
  }
}

###############################################################
# Monitoring Information
###############################################################

output "monitoring_info" {
  description = "Information for monitoring and alerting"
  value = {
    function_name        = module.lambda_function.lambda_function_name
    cloudwatch_namespace = "AWS/Lambda"
    log_group_name      = module.lambda_function.lambda_cloudwatch_log_group_name
    key_metrics = [
      "Duration",
      "Errors",
      "Invocations",
      "Throttles",
      "DeadLetterErrors",
      "IteratorAge",
      "ConcurrentExecutions",
      "UnreservedConcurrentExecutions"
    ]
    alarm_names = {
      error_rate = aws_cloudwatch_metric_alarm.error_rate.alarm_name
      duration   = aws_cloudwatch_metric_alarm.duration.alarm_name
      throttles  = aws_cloudwatch_metric_alarm.throttles.alarm_name
    }
  }
}

###############################################################
# Security Information
###############################################################

output "security_info" {
  description = "Security configuration information"
  value = {
    iam_role_arn           = module.lambda_function.lambda_role_arn
    kms_key_arn           = var.kms_key_arn
    vpc_configured        = var.vpc_config != null
    vpc_subnet_ids        = var.vpc_config != null ? var.vpc_config.subnet_ids : []
    vpc_security_group_ids = var.vpc_config != null ? var.vpc_config.security_group_ids : []
    tracing_enabled       = var.tracing_config_mode == "Active"
    environment_encrypted = var.kms_key_arn != ""
    dead_letter_configured = var.dead_letter_config != null
  }
}

###############################################################
# Integration Information
###############################################################

output "integration_info" {
  description = "Integration configuration information"
  value = {
    invoke_arn              = module.lambda_function.lambda_function_invoke_arn
    function_url           = module.lambda_function.lambda_function_url
    event_source_mappings  = length(var.event_source_mapping)
    layers                 = var.layers
    destination_on_failure = var.destination_config != null && var.destination_config.on_failure != null ? var.destination_config.on_failure.destination : null
    destination_on_success = var.destination_config != null && var.destination_config.on_success != null ? var.destination_config.on_success.destination : null
  }
}

###############################################################
# Cost Information
###############################################################

output "cost_info" {
  description = "Cost-related information"
  value = {
    memory_size             = var.memory_size
    timeout                = var.timeout
    reserved_concurrency   = var.reserved_concurrent_executions
    provisioned_concurrency = var.provisioned_concurrency_config != null ? var.provisioned_concurrency_config.provisioned_concurrent_executions : 0
    architecture           = var.architectures[0]
    estimated_monthly_requests = "Variable - depends on usage"
    cost_optimization_notes = [
      "Consider ARM64 architecture for better price-performance",
      "Right-size memory allocation based on actual usage",
      "Use provisioned concurrency only when needed",
      "Monitor and optimize timeout settings"
    ]
  }
}

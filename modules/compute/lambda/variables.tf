# Lambda Function Module Variables

###############################################################
# Required Pass-through Variables
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9]{2,5}$", var.application_abbreviated))
    error_message = "Application abbreviation must be 2-5 characters, lowercase letters and numbers only."
  }
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string
  default     = "processing"

  validation {
    condition = contains([
      "web", "api", "database", "processing", "monitoring", "security"
    ], var.service)
    error_message = "Service must be one of: web, api, database, processing, monitoring, security."
  }
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string

  validation {
    condition     = length(var.component) > 0 && can(regex("^[a-z0-9-]+$", var.component))
    error_message = "Component name must be non-empty and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Name of the environment"
  type        = string

  validation {
    condition = contains([
      "production", "nonprod", "homenet", "qamain",
      "qarapid", "scratch", "uat"
    ], var.environment)
    error_message = "Environment must be one of: production, nonprod, homenet, qamain, qarapid, scratch, uat."
  }
}

variable "region" {
  description = "AWS Region"
  type        = string

  validation {
    condition = contains([
      "us-east-1", "us-west-2"
    ], var.region)
    error_message = "Region must be one of the supported regions: us-east-1, us-west-2."
  }
}

variable "region_abbreviated" {
  description = "Abbreviated form of the AWS region"
  type        = string

  validation {
    condition = contains([
      "ue1", "uw2"
    ], var.region_abbreviated)
    error_message = "Region abbreviation must be one of: ue1, uw2."
  }
}

variable "build_number" {
  description = "Build Number for tracking deployments"
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.build_number))
    error_message = "Build number must be in semantic version format (e.g., 1.0.0)."
  }
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string

  validation {
    condition     = length(var.launched_by) > 0
    error_message = "Launched by cannot be empty."
  }
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string

  validation {
    condition     = can(formatdate("RFC3339", var.launched_on))
    error_message = "Launched on must be a valid RFC3339 timestamp."
  }
}

variable "slack_contact" {
  description = "Slack channel for notifications"
  type        = string

  validation {
    condition     = can(regex("^[+#@][a-zA-Z0-9-_]+$", var.slack_contact))
    error_message = "Slack contact must start with +, #, or @ followed by valid channel/user name."
  }
}

###############################################################
# Lambda Function Configuration
###############################################################

variable "function_name" {
  description = "Name of the Lambda function"
  type        = string
  default     = ""
}

variable "description" {
  description = "Description of the Lambda function"
  type        = string
  default     = ""
}

variable "runtime" {
  description = "Runtime for the Lambda function"
  type        = string
  default     = "python3.11"

  validation {
    condition = contains([
      "python3.8", "python3.9", "python3.10", "python3.11", "python3.12",
      "nodejs18.x", "nodejs20.x",
      "java8", "java11", "java17", "java21",
      "dotnet6", "dotnet8",
      "go1.x",
      "ruby3.2", "ruby3.3",
      "provided.al2", "provided.al2023"
    ], var.runtime)
    error_message = "Runtime must be a valid Lambda runtime."
  }
}

variable "handler" {
  description = "Function entrypoint in your code"
  type        = string
  default     = "lambda_function.lambda_handler"
}

variable "timeout" {
  description = "Amount of time your Lambda Function has to run in seconds"
  type        = number
  default     = 30

  validation {
    condition     = var.timeout >= 1 && var.timeout <= 900
    error_message = "Timeout must be between 1 and 900 seconds."
  }
}

variable "memory_size" {
  description = "Amount of memory in MB your Lambda Function can use at runtime"
  type        = number
  default     = 128

  validation {
    condition     = var.memory_size >= 128 && var.memory_size <= 10240
    error_message = "Memory size must be between 128 and 10240 MB."
  }
}

variable "reserved_concurrent_executions" {
  description = "Amount of reserved concurrent executions for this lambda function"
  type        = number
  default     = -1
}

variable "publish" {
  description = "Whether to publish creation/change as new Lambda Function Version"
  type        = bool
  default     = false
}

variable "package_type" {
  description = "Lambda deployment package type"
  type        = string
  default     = "Zip"

  validation {
    condition = contains([
      "Zip", "Image"
    ], var.package_type)
    error_message = "Package type must be Zip or Image."
  }
}

variable "architectures" {
  description = "Instruction set architecture for your Lambda function"
  type        = list(string)
  default     = ["x86_64"]

  validation {
    condition = alltrue([
      for arch in var.architectures :
      contains(["x86_64", "arm64"], arch)
    ])
    error_message = "Architectures must be x86_64 or arm64."
  }
}

###############################################################
# Source Code Configuration
###############################################################

variable "source_path" {
  description = "Path to the source code directory"
  type        = string
  default     = ""
}

variable "filename" {
  description = "Path to the function's deployment package within the local filesystem"
  type        = string
  default     = ""
}

variable "s3_bucket" {
  description = "S3 bucket location containing the function's deployment package"
  type        = string
  default     = ""
}

variable "s3_key" {
  description = "S3 key of an object containing the function's deployment package"
  type        = string
  default     = ""
}

variable "s3_object_version" {
  description = "Object version containing the function's deployment package"
  type        = string
  default     = ""
}

variable "image_uri" {
  description = "ECR image URI containing the function's deployment package"
  type        = string
  default     = ""
}

variable "image_config" {
  description = "Configuration for the container image"
  type = object({
    command           = optional(list(string), [])
    entry_point       = optional(list(string), [])
    working_directory = optional(string, "")
  })
  default = {}
}

###############################################################
# Environment Variables
###############################################################

variable "environment_variables" {
  description = "Map of environment variables for the Lambda function"
  type        = map(string)
  default     = {}
  sensitive   = true
}

variable "kms_key_arn" {
  description = "ARN of the KMS key used to encrypt environment variables"
  type        = string
  default     = ""
}

###############################################################
# VPC Configuration
###############################################################

variable "vpc_config" {
  description = "VPC configuration for the Lambda function"
  type = object({
    subnet_ids         = list(string)
    security_group_ids = list(string)
  })
  default = null
}

###############################################################
# Dead Letter Queue Configuration
###############################################################

variable "dead_letter_config" {
  description = "Dead letter queue configuration"
  type = object({
    target_arn = string
  })
  default = null
}

###############################################################
# Tracing Configuration
###############################################################

variable "tracing_config_mode" {
  description = "Tracing mode for AWS X-Ray"
  type        = string
  default     = "PassThrough"

  validation {
    condition = contains([
      "Active", "PassThrough"
    ], var.tracing_config_mode)
    error_message = "Tracing mode must be Active or PassThrough."
  }
}

###############################################################
# Layers Configuration
###############################################################

variable "layers" {
  description = "List of Lambda Layer Version ARNs to attach to your Lambda Function"
  type        = list(string)
  default     = []
}

###############################################################
# IAM Configuration
###############################################################

variable "create_role" {
  description = "Whether to create an IAM role for the Lambda function"
  type        = bool
  default     = true
}

variable "lambda_role" {
  description = "IAM role ARN for the Lambda function (if not creating one)"
  type        = string
  default     = ""
}

variable "attach_cloudwatch_logs_policy" {
  description = "Whether to attach CloudWatch Logs policy to the Lambda role"
  type        = bool
  default     = true
}

variable "attach_dead_letter_policy" {
  description = "Whether to attach dead letter queue policy to the Lambda role"
  type        = bool
  default     = false
}

variable "attach_network_policy" {
  description = "Whether to attach VPC/network policy to the Lambda role"
  type        = bool
  default     = false
}

variable "attach_tracing_policy" {
  description = "Whether to attach X-Ray tracing policy to the Lambda role"
  type        = bool
  default     = false
}

variable "attach_async_event_policy" {
  description = "Whether to attach async event policy to the Lambda role"
  type        = bool
  default     = false
}

variable "additional_iam_policies" {
  description = "List of additional IAM policy ARNs to attach to the Lambda role"
  type        = list(string)
  default     = []
}

variable "policy_statements" {
  description = "Map of dynamic policy statements to attach to Lambda function role"
  type        = any
  default     = {}
}

###############################################################
# Event Source Mapping Configuration
###############################################################

variable "event_source_mapping" {
  description = "Map of event source mapping configurations"
  type = map(object({
    event_source_arn                   = string
    function_name                      = optional(string, "")
    enabled                           = optional(bool, true)
    batch_size                        = optional(number, 10)
    maximum_batching_window_in_seconds = optional(number, 0)
    parallelization_factor            = optional(number, 1)
    starting_position                 = optional(string, "LATEST")
    starting_position_timestamp       = optional(string, "")
    maximum_record_age_in_seconds     = optional(number, -1)
    bisect_batch_on_function_error    = optional(bool, false)
    maximum_retry_attempts            = optional(number, -1)
    tumbling_window_in_seconds        = optional(number, 0)
    topics                            = optional(list(string), [])
    queues                            = optional(list(string), [])
    source_access_configuration = optional(list(object({
      type = string
      uri  = string
    })), [])
    self_managed_event_source = optional(object({
      endpoints = map(string)
    }), null)
    amazon_managed_kafka_event_source_config = optional(object({
      consumer_group_id = string
    }), null)
    self_managed_kafka_event_source_config = optional(object({
      consumer_group_id = string
    }), null)
    function_response_types = optional(list(string), [])
  }))
  default = {}
}

###############################################################
# CloudWatch Configuration
###############################################################

variable "cloudwatch_logs_retention_in_days" {
  description = "Specifies the number of days you want to retain log events in the specified log group"
  type        = number
  default     = 14

  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.cloudwatch_logs_retention_in_days)
    error_message = "CloudWatch logs retention must be a valid value."
  }
}

variable "cloudwatch_logs_kms_key_id" {
  description = "The ARN of the KMS Key to use when encrypting log data"
  type        = string
  default     = ""
}

variable "create_cloudwatch_log_group" {
  description = "Whether to create CloudWatch log group for the Lambda function"
  type        = bool
  default     = true
}

###############################################################
# Provisioned Concurrency Configuration
###############################################################

variable "provisioned_concurrency_config" {
  description = "Provisioned concurrency configuration"
  type = object({
    provisioned_concurrent_executions = number
    qualifier                         = optional(string, "$LATEST")
  })
  default = null
}

###############################################################
# Async Configuration
###############################################################

variable "destination_config" {
  description = "Configuration for destinations of asynchronous invocations"
  type = object({
    on_failure = optional(object({
      destination = string
    }), null)
    on_success = optional(object({
      destination = string
    }), null)
  })
  default = null
}

variable "maximum_event_age_in_seconds" {
  description = "Maximum age of a request that Lambda sends to a function for processing in seconds"
  type        = number
  default     = 21600

  validation {
    condition     = var.maximum_event_age_in_seconds >= 60 && var.maximum_event_age_in_seconds <= 21600
    error_message = "Maximum event age must be between 60 and 21600 seconds."
  }
}

variable "maximum_retry_attempts" {
  description = "Maximum number of times to retry when the function returns an error"
  type        = number
  default     = 2

  validation {
    condition     = var.maximum_retry_attempts >= 0 && var.maximum_retry_attempts <= 2
    error_message = "Maximum retry attempts must be between 0 and 2."
  }
}

###############################################################
# Common Resource Tags
###############################################################

locals {
  common_tags = {
    Application  = var.application
    Service      = var.service
    Component    = var.component
    Environment  = var.environment
    Region       = var.region
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    ManagedBy    = "terraform"
    Module       = "lambda"
  }

  # Generate function name
  function_name = var.function_name != "" ? var.function_name : "${var.application}-${var.component}-${var.environment}"

  # Generate description
  function_description = var.description != "" ? var.description : "Lambda function for ${var.application} ${var.component} in ${var.environment}"

  # Determine if VPC configuration is needed
  attach_network_policy = var.vpc_config != null ? true : var.attach_network_policy

  # Determine if tracing policy is needed
  attach_tracing_policy = var.tracing_config_mode == "Active" ? true : var.attach_tracing_policy

  # Determine if dead letter policy is needed
  attach_dead_letter_policy = var.dead_letter_config != null ? true : var.attach_dead_letter_policy
}

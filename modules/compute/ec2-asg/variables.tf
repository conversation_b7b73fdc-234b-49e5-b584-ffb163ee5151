# EC2 Auto Scaling Group Module Variables

###############################################################
# Required Pass-through Variables
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9]{2,5}$", var.application_abbreviated))
    error_message = "Application abbreviation must be 2-5 characters, lowercase letters and numbers only."
  }
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string
  default     = "web"

  validation {
    condition = contains([
      "web", "api", "database", "processing", "monitoring", "security"
    ], var.service)
    error_message = "Service must be one of: web, api, database, processing, monitoring, security."
  }
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string

  validation {
    condition     = length(var.component) > 0 && can(regex("^[a-z0-9-]+$", var.component))
    error_message = "Component name must be non-empty and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Name of the environment"
  type        = string

  validation {
    condition = contains([
      "production", "nonprod", "homenet", "qamain",
      "qarapid", "scratch", "uat"
    ], var.environment)
    error_message = "Environment must be one of: production, nonprod, homenet, qamain, qarapid, scratch, uat."
  }
}

variable "region" {
  description = "AWS Region"
  type        = string

  validation {
    condition = contains([
      "us-east-1", "us-west-2"
    ], var.region)
    error_message = "Region must be one of the supported regions: us-east-1, us-west-2."
  }
}

variable "region_abbreviated" {
  description = "Abbreviated form of the AWS region"
  type        = string

  validation {
    condition = contains([
      "ue1", "uw2"
    ], var.region_abbreviated)
    error_message = "Region abbreviation must be one of: ue1, uw2."
  }
}

variable "build_number" {
  description = "Build Number for tracking deployments"
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.build_number))
    error_message = "Build number must be in semantic version format (e.g., 1.0.0)."
  }
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string

  validation {
    condition     = length(var.launched_by) > 0
    error_message = "Launched by cannot be empty."
  }
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string

  validation {
    condition     = can(formatdate("RFC3339", var.launched_on))
    error_message = "Launched on must be a valid RFC3339 timestamp."
  }
}

variable "slack_contact" {
  description = "Slack channel for notifications"
  type        = string

  validation {
    condition     = can(regex("^[+#@][a-zA-Z0-9-_]+$", var.slack_contact))
    error_message = "Slack contact must start with +, #, or @ followed by valid channel/user name."
  }
}

###############################################################
# Network Configuration
###############################################################

variable "vpc_id" {
  description = "ID of the VPC where instances will be created"
  type        = string

  validation {
    condition     = can(regex("^vpc-[a-z0-9]+$", var.vpc_id))
    error_message = "VPC ID must be a valid AWS VPC identifier."
  }
}

variable "subnet_ids" {
  description = "List of subnet IDs for the Auto Scaling Group"
  type        = list(string)

  validation {
    condition     = length(var.subnet_ids) >= 1
    error_message = "At least 1 subnet ID must be provided."
  }
}

variable "security_group_ids" {
  description = "List of security group IDs to associate with instances"
  type        = list(string)
  default     = []
}

###############################################################
# Instance Configuration
###############################################################

variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "t3.medium"

  validation {
    condition = contains([
      "t3.micro", "t3.small", "t3.medium", "t3.large", "t3.xlarge", "t3.2xlarge",
      "t3a.micro", "t3a.small", "t3a.medium", "t3a.large", "t3a.xlarge", "t3a.2xlarge",
      "m5.large", "m5.xlarge", "m5.2xlarge", "m5.4xlarge", "m5.8xlarge", "m5.12xlarge",
      "m5a.large", "m5a.xlarge", "m5a.2xlarge", "m5a.4xlarge", "m5a.8xlarge", "m5a.12xlarge",
      "c5.large", "c5.xlarge", "c5.2xlarge", "c5.4xlarge", "c5.9xlarge", "c5.12xlarge",
      "r5.large", "r5.xlarge", "r5.2xlarge", "r5.4xlarge", "r5.8xlarge", "r5.12xlarge"
    ], var.instance_type)
    error_message = "Instance type must be a valid EC2 instance type."
  }
}

variable "ami_id" {
  description = "AMI ID to use for instances (if not provided, latest Amazon Linux 2 will be used)"
  type        = string
  default     = ""
}

variable "ami_name_filter" {
  description = "Name filter for AMI lookup"
  type        = string
  default     = "amzn2-ami-hvm-*-x86_64-gp2"
}

variable "ami_owner" {
  description = "Owner of the AMI"
  type        = string
  default     = "amazon"
}

variable "key_name" {
  description = "Name of the EC2 Key Pair for SSH access"
  type        = string
  default     = ""
}

variable "associate_public_ip_address" {
  description = "Whether to associate a public IP address with instances"
  type        = bool
  default     = false
}

variable "enable_monitoring" {
  description = "Enable detailed monitoring for instances"
  type        = bool
  default     = true
}

variable "ebs_optimized" {
  description = "Enable EBS optimization for instances"
  type        = bool
  default     = true
}

###############################################################
# Storage Configuration
###############################################################

variable "root_volume_size" {
  description = "Size of the root volume in GB"
  type        = number
  default     = 20

  validation {
    condition     = var.root_volume_size >= 8 && var.root_volume_size <= 16384
    error_message = "Root volume size must be between 8 and 16384 GB."
  }
}

variable "root_volume_type" {
  description = "Type of the root volume"
  type        = string
  default     = "gp3"

  validation {
    condition = contains([
      "gp2", "gp3", "io1", "io2", "sc1", "st1"
    ], var.root_volume_type)
    error_message = "Root volume type must be a valid EBS volume type."
  }
}

variable "root_volume_encrypted" {
  description = "Whether to encrypt the root volume"
  type        = bool
  default     = true
}

variable "root_volume_kms_key_id" {
  description = "KMS key ID for root volume encryption"
  type        = string
  default     = ""
}

variable "additional_ebs_volumes" {
  description = "Additional EBS volumes to attach to instances"
  type = list(object({
    device_name = string
    volume_size = number
    volume_type = string
    encrypted   = optional(bool, true)
    kms_key_id  = optional(string, "")
    iops        = optional(number, null)
    throughput  = optional(number, null)
  }))
  default = []
}

###############################################################
# Auto Scaling Configuration
###############################################################

variable "min_size" {
  description = "Minimum number of instances in the Auto Scaling Group"
  type        = number
  default     = 1

  validation {
    condition     = var.min_size >= 0
    error_message = "Minimum size must be greater than or equal to 0."
  }
}

variable "max_size" {
  description = "Maximum number of instances in the Auto Scaling Group"
  type        = number
  default     = 3

  validation {
    condition     = var.max_size >= 1
    error_message = "Maximum size must be greater than or equal to 1."
  }
}

variable "desired_capacity" {
  description = "Desired number of instances in the Auto Scaling Group"
  type        = number
  default     = 2

  validation {
    condition     = var.desired_capacity >= 0
    error_message = "Desired capacity must be greater than or equal to 0."
  }
}

variable "health_check_type" {
  description = "Type of health check for Auto Scaling Group"
  type        = string
  default     = "EC2"

  validation {
    condition = contains([
      "EC2", "ELB"
    ], var.health_check_type)
    error_message = "Health check type must be EC2 or ELB."
  }
}

variable "health_check_grace_period" {
  description = "Time after instance launch before checking health"
  type        = number
  default     = 300

  validation {
    condition     = var.health_check_grace_period >= 0
    error_message = "Health check grace period must be greater than or equal to 0."
  }
}

variable "default_cooldown" {
  description = "Default cooldown period for Auto Scaling Group"
  type        = number
  default     = 300

  validation {
    condition     = var.default_cooldown >= 0
    error_message = "Default cooldown must be greater than or equal to 0."
  }
}

variable "termination_policies" {
  description = "List of termination policies for the Auto Scaling Group"
  type        = list(string)
  default     = ["Default"]

  validation {
    condition = alltrue([
      for policy in var.termination_policies :
      contains(["Default", "OldestInstance", "NewestInstance", "OldestLaunchConfiguration", "OldestLaunchTemplate", "ClosestToNextInstanceHour"], policy)
    ])
    error_message = "Termination policies must be valid ASG termination policies."
  }
}

variable "suspended_processes" {
  description = "List of processes to suspend for the Auto Scaling Group"
  type        = list(string)
  default     = []

  validation {
    condition = alltrue([
      for process in var.suspended_processes :
      contains(["Launch", "Terminate", "HealthCheck", "ReplaceUnhealthy", "AZRebalance", "AlarmNotification", "ScheduledActions", "AddToLoadBalancer"], process)
    ])
    error_message = "Suspended processes must be valid ASG processes."
  }
}

variable "enabled_metrics" {
  description = "List of metrics to enable for the Auto Scaling Group"
  type        = list(string)
  default = [
    "GroupMinSize",
    "GroupMaxSize",
    "GroupDesiredCapacity",
    "GroupInServiceInstances",
    "GroupTotalInstances"
  ]
}

variable "protect_from_scale_in" {
  description = "Whether instances are protected from scale-in"
  type        = bool
  default     = false
}

###############################################################
# Load Balancer Integration
###############################################################

variable "target_group_arns" {
  description = "List of target group ARNs to associate with the Auto Scaling Group"
  type        = list(string)
  default     = []
}

variable "load_balancers" {
  description = "List of classic load balancer names to associate with the Auto Scaling Group"
  type        = list(string)
  default     = []
}

###############################################################
# User Data Configuration
###############################################################

variable "user_data_base64" {
  description = "Base64-encoded user data script"
  type        = string
  default     = ""
}

variable "user_data_script" {
  description = "User data script (will be base64 encoded)"
  type        = string
  default     = ""
}

variable "enable_cloudwatch_agent" {
  description = "Whether to install and configure CloudWatch agent"
  type        = bool
  default     = true
}

variable "enable_ssm_agent" {
  description = "Whether to install and configure SSM agent"
  type        = bool
  default     = true
}

###############################################################
# IAM Configuration
###############################################################

variable "create_iam_instance_profile" {
  description = "Whether to create an IAM instance profile"
  type        = bool
  default     = true
}

variable "iam_instance_profile_name" {
  description = "Name of existing IAM instance profile (if not creating one)"
  type        = string
  default     = ""
}

variable "additional_iam_policies" {
  description = "List of additional IAM policy ARNs to attach to the instance role"
  type        = list(string)
  default     = []
}

###############################################################
# Scaling Policies
###############################################################

variable "enable_scaling_policies" {
  description = "Whether to create scaling policies"
  type        = bool
  default     = true
}

variable "scale_up_cooldown" {
  description = "Cooldown period for scale up actions"
  type        = number
  default     = 300
}

variable "scale_down_cooldown" {
  description = "Cooldown period for scale down actions"
  type        = number
  default     = 300
}

variable "scale_up_adjustment" {
  description = "Number of instances to add during scale up"
  type        = number
  default     = 1
}

variable "scale_down_adjustment" {
  description = "Number of instances to remove during scale down"
  type        = number
  default     = -1
}

variable "cpu_utilization_high_threshold" {
  description = "CPU utilization threshold for scale up"
  type        = number
  default     = 80

  validation {
    condition     = var.cpu_utilization_high_threshold >= 0 && var.cpu_utilization_high_threshold <= 100
    error_message = "CPU utilization threshold must be between 0 and 100."
  }
}

variable "cpu_utilization_low_threshold" {
  description = "CPU utilization threshold for scale down"
  type        = number
  default     = 20

  validation {
    condition     = var.cpu_utilization_low_threshold >= 0 && var.cpu_utilization_low_threshold <= 100
    error_message = "CPU utilization threshold must be between 0 and 100."
  }
}

###############################################################
# Common Resource Tags
###############################################################

locals {
  common_tags = {
    Application  = var.application
    Service      = var.service
    Component    = var.component
    Environment  = var.environment
    Region       = var.region
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    ManagedBy    = "terraform"
    Module       = "ec2-asg"
  }

  # Generate names
  name_prefix = "${var.application}-${var.component}-${var.environment}"

  # User data script
  user_data = var.user_data_base64 != "" ? var.user_data_base64 : (
    var.user_data_script != "" ? base64encode(var.user_data_script) : base64encode(templatefile("${path.module}/user-data.sh", {
      enable_cloudwatch_agent = var.enable_cloudwatch_agent
      enable_ssm_agent        = var.enable_ssm_agent
      application            = var.application
      environment            = var.environment
      component              = var.component
    }))
  )
}

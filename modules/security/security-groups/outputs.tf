# Security Groups Module Outputs

###############################################################
# Security Group Outputs
###############################################################

output "security_group_ids" {
  description = "Map of security group names to their IDs"
  value = {
    for key, sg in aws_security_group.this : key => sg.id
  }
}

output "security_group_arns" {
  description = "Map of security group names to their ARNs"
  value = {
    for key, sg in aws_security_group.this : key => sg.arn
  }
}

output "security_group_names" {
  description = "Map of security group keys to their actual names"
  value = {
    for key, sg in aws_security_group.this : key => sg.name
  }
}

output "security_groups" {
  description = "Complete security group objects"
  value = aws_security_group.this
}

###############################################################
# Individual Security Group Outputs (for common patterns)
###############################################################

output "web_server_sg_id" {
  description = "ID of the web server security group (if created)"
  value       = contains(keys(var.security_groups), "web-server") ? aws_security_group.this["web-server"].id : null
}

output "database_sg_id" {
  description = "ID of the database security group (if created)"
  value       = contains(keys(var.security_groups), "database") ? aws_security_group.this["database"].id : null
}

output "load_balancer_sg_id" {
  description = "ID of the load balancer security group (if created)"
  value       = contains(keys(var.security_groups), "load-balancer") ? aws_security_group.this["load-balancer"].id : null
}

output "ecs_sg_id" {
  description = "ID of the ECS security group (if created)"
  value       = contains(keys(var.security_groups), "ecs") ? aws_security_group.this["ecs"].id : null
}

###############################################################
# Security Group Rules Summary
###############################################################

output "ingress_rules_count" {
  description = "Total number of ingress rules created"
  value       = length(aws_security_group_rule.ingress)
}

output "egress_rules_count" {
  description = "Total number of egress rules created"
  value       = length(aws_security_group_rule.egress)
}

output "security_groups_count" {
  description = "Total number of security groups created"
  value       = length(aws_security_group.this)
}

###############################################################
# Configuration Summary
###############################################################

output "security_configuration" {
  description = "Summary of security group configuration"
  value = {
    vpc_id                = var.vpc_id
    security_groups_count = length(aws_security_group.this)
    ingress_rules_count   = length(aws_security_group_rule.ingress)
    egress_rules_count    = length(aws_security_group_rule.egress)
    trusted_cidrs         = local.trusted_cidrs
    all_trusted_cidrs     = local.all_trusted_cidrs
    security_group_names  = [for sg in aws_security_group.this : sg.name]
  }
}

###############################################################
# Security Group Templates (for reference)
###############################################################

output "security_group_templates" {
  description = "Available security group templates for reference"
  value = {
    web_server     = local.web_server_sg
    database       = local.database_sg
    load_balancer  = local.load_balancer_sg
  }
}

###############################################################
# Network Configuration
###############################################################

output "network_configuration" {
  description = "Network configuration used for security groups"
  value = {
    vpc_id            = var.vpc_id
    homenet_cidr      = var.homenet_cidr
    ais_cidr          = var.ais_cidr
    remote_cidr       = var.remote_cidr
    nfs_cidr          = var.nfs_cidr
    ground_dc_cidrs   = var.ground_dc_cidrs
    trusted_cidrs     = local.trusted_cidrs
    all_trusted_cidrs = local.all_trusted_cidrs
  }
}

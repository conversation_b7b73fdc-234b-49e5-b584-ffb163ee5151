# Security Groups Module Variables

###############################################################
# Required Pass-through Variables
###############################################################

variable "application" {
  description = "Name of the application"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9]{2,5}$", var.application_abbreviated))
    error_message = "Application abbreviation must be 2-5 characters, lowercase letters and numbers only."
  }
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string

  validation {
    condition = contains([
      "web", "api", "database", "processing", "monitoring", "security"
    ], var.service)
    error_message = "Service must be one of: web, api, database, processing, monitoring, security."
  }
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string

  validation {
    condition     = length(var.component) > 0 && can(regex("^[a-z0-9-]+$", var.component))
    error_message = "Component name must be non-empty and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Name of the environment"
  type        = string

  validation {
    condition = contains([
      "production", "nonprod", "homenet", "qamain",
      "qarapid", "scratch", "uat"
    ], var.environment)
    error_message = "Environment must be one of: production, nonprod, homenet, qamain, qarapid, scratch, uat."
  }
}

variable "region" {
  description = "AWS Region"
  type        = string

  validation {
    condition = contains([
      "us-east-1", "us-west-2"
    ], var.region)
    error_message = "Region must be one of the supported regions: us-east-1, us-west-2."
  }
}

variable "region_abbreviated" {
  description = "Abbreviated form of the AWS region"
  type        = string

  validation {
    condition = contains([
      "ue1", "uw2"
    ], var.region_abbreviated)
    error_message = "Region abbreviation must be one of: ue1, uw2."
  }
}

variable "build_number" {
  description = "Build Number for tracking deployments"
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.build_number))
    error_message = "Build number must be in semantic version format (e.g., 1.0.0)."
  }
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string

  validation {
    condition     = length(var.launched_by) > 0
    error_message = "Launched by cannot be empty."
  }
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string

  validation {
    condition     = can(formatdate("RFC3339", var.launched_on))
    error_message = "Launched on must be a valid RFC3339 timestamp."
  }
}

variable "slack_contact" {
  description = "Slack channel for notifications"
  type        = string

  validation {
    condition     = can(regex("^[+#@][a-zA-Z0-9-_]+$", var.slack_contact))
    error_message = "Slack contact must start with +, #, or @ followed by valid channel/user name."
  }
}

###############################################################
# VPC Configuration
###############################################################

variable "vpc_id" {
  description = "ID of the VPC where security groups will be created"
  type        = string

  validation {
    condition     = can(regex("^vpc-[a-z0-9]+$", var.vpc_id))
    error_message = "VPC ID must be a valid AWS VPC identifier."
  }
}

###############################################################
# Network Security Variables
###############################################################

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network for secure access"
  type        = string

  validation {
    condition     = can(cidrhost(var.homenet_cidr, 0))
    error_message = "HomeNet CIDR must be a valid CIDR block."
  }
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network for secure access"
  type        = string

  validation {
    condition     = can(cidrhost(var.ais_cidr, 0))
    error_message = "AIS CIDR must be a valid CIDR block."
  }
}

variable "remote_cidr" {
  description = "The public CIDR block of the Remote networks for secure access"
  type        = string

  validation {
    condition     = can(cidrhost(var.remote_cidr, 0))
    error_message = "Remote CIDR must be a valid CIDR block."
  }
}

variable "nfs_cidr" {
  description = "The CIDR block of the internal AWS networks for NFS usage"
  type        = string

  validation {
    condition     = can(cidrhost(var.nfs_cidr, 0))
    error_message = "NFS CIDR must be a valid CIDR block."
  }
}

variable "ground_dc_cidrs" {
  description = "List of ground data center CIDR blocks for secure access"
  type        = list(string)
  default     = []

  validation {
    condition = alltrue([
      for cidr in var.ground_dc_cidrs : can(cidrhost(cidr, 0))
    ])
    error_message = "All ground DC CIDRs must be valid CIDR blocks."
  }
}

###############################################################
# Security Group Configuration
###############################################################

variable "security_groups" {
  description = "Map of security groups to create with their configurations"
  type = map(object({
    description = string
    ingress_rules = optional(list(object({
      description              = string
      from_port               = number
      to_port                 = number
      protocol                = string
      cidr_blocks             = optional(list(string), [])
      ipv6_cidr_blocks        = optional(list(string), [])
      security_groups         = optional(list(string), [])
      self                    = optional(bool, false)
      prefix_list_ids         = optional(list(string), [])
    })), [])
    egress_rules = optional(list(object({
      description              = string
      from_port               = number
      to_port                 = number
      protocol                = string
      cidr_blocks             = optional(list(string), [])
      ipv6_cidr_blocks        = optional(list(string), [])
      security_groups         = optional(list(string), [])
      self                    = optional(bool, false)
      prefix_list_ids         = optional(list(string), [])
    })), [])
    tags = optional(map(string), {})
  }))
  default = {}
}

###############################################################
# Common Resource Tags
###############################################################

locals {
  common_tags = {
    Application  = var.application
    Service      = var.service
    Component    = var.component
    Environment  = var.environment
    Region       = var.region
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    ManagedBy    = "terraform"
    Module       = "security-groups"
  }

  # Trusted network CIDRs for common use
  trusted_cidrs = [
    var.homenet_cidr,
    var.ais_cidr,
    var.remote_cidr
  ]

  # All trusted CIDRs including ground DCs
  all_trusted_cidrs = concat(local.trusted_cidrs, var.ground_dc_cidrs)
}

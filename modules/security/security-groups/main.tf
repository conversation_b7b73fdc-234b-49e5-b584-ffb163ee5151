# Security Groups Module
# This module provides standardized security group creation with best practices

###############################################################
# Security Groups
###############################################################

resource "aws_security_group" "this" {
  for_each = var.security_groups

  name        = "${var.application}-${var.component}-${var.environment}-${each.key}"
  description = each.value.description
  vpc_id      = var.vpc_id

  # Lifecycle management
  lifecycle {
    create_before_destroy = true
  }

  # Common tags merged with custom tags
  tags = merge(
    local.common_tags,
    each.value.tags,
    {
      Name = "sg-${each.key}-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
      Type = "SecurityGroup"
    }
  )
}

###############################################################
# Ingress Rules
###############################################################

resource "aws_security_group_rule" "ingress" {
  for_each = {
    for rule in flatten([
      for sg_key, sg_config in var.security_groups : [
        for idx, rule in sg_config.ingress_rules : {
          key                      = "${sg_key}-ingress-${idx}"
          security_group_id        = aws_security_group.this[sg_key].id
          type                     = "ingress"
          description              = rule.description
          from_port               = rule.from_port
          to_port                 = rule.to_port
          protocol                = rule.protocol
          cidr_blocks             = rule.cidr_blocks
          ipv6_cidr_blocks        = rule.ipv6_cidr_blocks
          source_security_group_id = length(rule.security_groups) > 0 ? rule.security_groups[0] : null
          self                    = rule.self
          prefix_list_ids         = rule.prefix_list_ids
        }
      ]
    ]) : rule.key => rule
  }

  security_group_id        = each.value.security_group_id
  type                     = each.value.type
  description              = each.value.description
  from_port               = each.value.from_port
  to_port                 = each.value.to_port
  protocol                = each.value.protocol
  cidr_blocks             = length(each.value.cidr_blocks) > 0 ? each.value.cidr_blocks : null
  ipv6_cidr_blocks        = length(each.value.ipv6_cidr_blocks) > 0 ? each.value.ipv6_cidr_blocks : null
  source_security_group_id = each.value.source_security_group_id
  self                    = each.value.self
  prefix_list_ids         = length(each.value.prefix_list_ids) > 0 ? each.value.prefix_list_ids : null
}

###############################################################
# Egress Rules
###############################################################

resource "aws_security_group_rule" "egress" {
  for_each = {
    for rule in flatten([
      for sg_key, sg_config in var.security_groups : [
        for idx, rule in sg_config.egress_rules : {
          key                      = "${sg_key}-egress-${idx}"
          security_group_id        = aws_security_group.this[sg_key].id
          type                     = "egress"
          description              = rule.description
          from_port               = rule.from_port
          to_port                 = rule.to_port
          protocol                = rule.protocol
          cidr_blocks             = rule.cidr_blocks
          ipv6_cidr_blocks        = rule.ipv6_cidr_blocks
          source_security_group_id = length(rule.security_groups) > 0 ? rule.security_groups[0] : null
          self                    = rule.self
          prefix_list_ids         = rule.prefix_list_ids
        }
      ]
    ]) : rule.key => rule
  }

  security_group_id        = each.value.security_group_id
  type                     = each.value.type
  description              = each.value.description
  from_port               = each.value.from_port
  to_port                 = each.value.to_port
  protocol                = each.value.protocol
  cidr_blocks             = length(each.value.cidr_blocks) > 0 ? each.value.cidr_blocks : null
  ipv6_cidr_blocks        = length(each.value.ipv6_cidr_blocks) > 0 ? each.value.ipv6_cidr_blocks : null
  source_security_group_id = each.value.source_security_group_id
  self                    = each.value.self
  prefix_list_ids         = length(each.value.prefix_list_ids) > 0 ? each.value.prefix_list_ids : null
}

###############################################################
# Common Security Group Templates
###############################################################

# Web server security group template
locals {
  web_server_sg = {
    description = "Security group for web servers"
    ingress_rules = [
      {
        description = "HTTP from load balancer"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = []
        security_groups = [] # Should be populated with ALB security group
      },
      {
        description = "HTTPS from load balancer"
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = []
        security_groups = [] # Should be populated with ALB security group
      },
      {
        description = "SSH from trusted networks"
        from_port   = 22
        to_port     = 22
        protocol    = "tcp"
        cidr_blocks = local.trusted_cidrs
      }
    ]
    egress_rules = [
      {
        description = "All outbound traffic"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
  }

  # Database security group template
  database_sg = {
    description = "Security group for database servers"
    ingress_rules = [
      {
        description = "MySQL/Aurora from application servers"
        from_port   = 3306
        to_port     = 3306
        protocol    = "tcp"
        cidr_blocks = [var.nfs_cidr]
      },
      {
        description = "MySQL/Aurora from trusted networks"
        from_port   = 3306
        to_port     = 3306
        protocol    = "tcp"
        cidr_blocks = local.all_trusted_cidrs
      }
    ]
    egress_rules = [
      {
        description = "All outbound traffic"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
  }

  # Load balancer security group template
  load_balancer_sg = {
    description = "Security group for load balancers"
    ingress_rules = [
      {
        description = "HTTP from internet"
        from_port   = 80
        to_port     = 80
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      },
      {
        description = "HTTPS from internet"
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
    egress_rules = [
      {
        description = "All outbound traffic"
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = ["0.0.0.0/0"]
      }
    ]
  }
}

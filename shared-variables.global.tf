#==============================================================
# Shared Variables for All Accounts
#==============================================================

###############################################################
# Terraform and Provider Version Constraints
###############################################################

terraform {
  required_version = ">= 1.5.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

###############################################################
# Lookup Dictionaries
###############################################################

variable "regions_abbreviated" {
  description = "Mapping of AWS regions to their abbreviated forms"
  type        = map(string)
  default = {
    "us-east-1" = "ue1"
    "us-west-2" = "uw2"
  }

  validation {
    condition = alltrue([
      for region in keys(var.regions_abbreviated) :
      can(regex("^us-(east|west)-[0-9]$", region))
    ])
    error_message = "All regions must be valid US AWS regions (us-east-X or us-west-X)."
  }
}

###############################################################
# Required Variables from Outside
###############################################################

variable "environment" {
  description = "Name of the environment we are building"
  type        = string

  validation {
    condition = contains([
      "production", "nonprod", "homenet", "qamain",
      "qarapid", "scratch", "uat"
    ], var.environment)
    error_message = "Environment must be one of: production, nonprod, homenet, qamain, qarapid, scratch, uat."
  }
}

variable "build_number" {
  description = "Build Number for tracking deployments"
  type        = string

  validation {
    condition     = can(regex("^[0-9]+\\.[0-9]+\\.[0-9]+$", var.build_number))
    error_message = "Build number must be in semantic version format (e.g., 1.0.0)."
  }
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string

  validation {
    condition     = length(var.launched_by) > 0
    error_message = "Launched by cannot be empty."
  }
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string

  validation {
    condition     = can(formatdate("RFC3339", var.launched_on))
    error_message = "Launched on must be a valid RFC3339 timestamp."
  }
}

variable "region" {
  description = "AWS Region where resources will be deployed"
  type        = string

  validation {
    condition = contains([
      "us-east-1", "us-west-2"
    ], var.region)
    error_message = "Region must be one of the supported regions: us-east-1, us-west-2."
  }
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string

  validation {
    condition     = length(var.component) > 0 && can(regex("^[a-z0-9-]+$", var.component))
    error_message = "Component name must be non-empty and contain only lowercase letters, numbers, and hyphens."
  }
}

###############################################################
# Application Variables
###############################################################

variable "application" {
  description = "Name of the application to be used when tagging AWS resources"
  type        = string
  default     = "ais10"

  validation {
    condition     = can(regex("^[a-z0-9]+$", var.application))
    error_message = "Application name must contain only lowercase letters and numbers."
  }
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name used in resource naming"
  type        = string
  default     = "a10"

  validation {
    condition     = can(regex("^[a-z0-9]{2,5}$", var.application_abbreviated))
    error_message = "Application abbreviation must be 2-5 characters, lowercase letters and numbers only."
  }
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string
  default     = "web"

  validation {
    condition = contains([
      "web", "api", "database", "processing", "monitoring", "security"
    ], var.service)
    error_message = "Service must be one of: web, api, database, processing, monitoring, security."
  }
}

variable "slack_contact" {
  description = "Slack channel that should be notified by monitoring alerts"
  type        = string
  default     = "+ais-operations"

  validation {
    condition     = can(regex("^[+#@][a-zA-Z0-9-_]+$", var.slack_contact))
    error_message = "Slack contact must start with +, #, or @ followed by valid channel/user name."
  }
}

###############################################################
# Network Security Variables
###############################################################

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network for secure access"
  type        = string
  default     = "************/32"

  validation {
    condition     = can(cidrhost(var.homenet_cidr, 0))
    error_message = "HomeNet CIDR must be a valid CIDR block."
  }
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network for secure access"
  type        = string
  default     = "***********/32"

  validation {
    condition     = can(cidrhost(var.ais_cidr, 0))
    error_message = "AIS CIDR must be a valid CIDR block."
  }
}

variable "remote_cidr" {
  description = "The public CIDR block of the Remote networks for secure access"
  type        = string
  default     = "**************/32"

  validation {
    condition     = can(cidrhost(var.remote_cidr, 0))
    error_message = "Remote CIDR must be a valid CIDR block."
  }
}

variable "ground_dc_cidrs" {
  description = "List of ground data center CIDR blocks for secure access"
  type        = list(string)
  default     = ["************/32", "**************/32", "************/24"]

  validation {
    condition = alltrue([
      for cidr in var.ground_dc_cidrs : can(cidrhost(cidr, 0))
    ])
    error_message = "All ground DC CIDRs must be valid CIDR blocks."
  }
}

###############################################################
# Route 53 Variables
###############################################################

variable "internal_domain" {
  description = "Domain to use when creating internal dns records"
  default     = "ais-internal.com"
}

variable "s3_website_hosted_zone_id" {
  description = "The Amazon AWS hosted zone id for s3 website hosting https://docs.aws.amazon.com/general/latest/gr/rande.html#s3_website_region_endpoints"
  type        = map(string)
  default = {
    "us-east-1" = "Z3AQBSTGFYJSTF"
    "us-west-2" = "Z3BJ6K6RIION7M"
  }
}

variable "processing_apps_component_id" {
  description = "The Component ID of the processing apps"
  default     = "CI0934608"
}

variable "internal_webstack_component_id" {
  description = "The Component ID of the internal-webstack"
  default     = "CI0941858"
}

variable "consumer_webstack_component_id" {
  description = "The Component ID of the consumer-webstack"
  default     = "CI0941859"
}
